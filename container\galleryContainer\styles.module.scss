@import "../../assets/css/global.scss";

.gallery_container{
    width: 100%;
    height: auto;
    padding: 50px 120px;

    .gallery_content{
        width: 100%;
        height: auto;
        display: flex;
        gap: 40px;

        .left_menu_container{
            background-color: #571F0B;
            min-width: 220px;
            height: auto;
            padding: 36px 32px;
            border-radius: 12px;

            .left_header{
                color: #ffffff;
                font-size: 16px;
                font-family: $poppinsRegular400;
                border-bottom: 2px solid white;
                padding-bottom: 8px;
                margin-bottom: 30px;
            }

            .menus{
                .menu_btns{
                    background-color: transparent;
                    border: none;
                    outline: none;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    color: white;
                    font-family: $poppinsRegular400;
                    font-size: 16px;
                    margin-bottom: 20px;
                    cursor: pointer;

                    &:hover{
                        margin-left: 10px;
                        opacity: 0.7;
                    }
                }
            }
        }

        .right_tabs_content{
            border: 1px solid #571F0B;
            border-radius: 12px;
            width: 100%;
            padding: 39px 43px;

            .top_tab_btns{
                display: flex;
                justify-content: flex-start;
                flex-wrap: wrap;
                gap: 15px 25px;
                border-bottom: 1px solid #571F0B;
                margin-bottom: 30px;
                padding-bottom: 30px;

                .tab_btn{
                    border-radius: 6px;
                    border: 1px solid #571F0B;
                    background-color: unset;
                    padding: 8px 24px;
                    color: #571F0B;
                    font-size: 14px;
                    font-family: $poppinsMedium500;
                    cursor: pointer;

                    &.active_tab_btn{
                        background-color: #571F0B;
                        color: white;
                    }

                      &:hover{
                        scale: 0.9;
                    }
                }
            }

            .tab_body{
                .cards_wrapper{
                    display: flex;
                    justify-content: flex-start;
                    flex-wrap: wrap;
                    gap: 20px;
                }
            }
        }
    }
}