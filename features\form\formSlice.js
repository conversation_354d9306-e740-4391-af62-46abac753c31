import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  studentCategory: "",
  oldRegNumber: "",
  regYear: "",
  schoolCategory: "",   
  schoolName: "",
  schoolCode: "",       
  district: "",    
  nodalSchool: "",
  schoolAddress: "",
  nonGovSchool: "",
  studentName: { english: "", hindi: "" },
  fatherName: { english: "", hindi: "" },
  motherName: { english: "", hindi: "" },
  dob: "",
  gender: "",
  caste: "",
  mobile: "",
  email: "",
  compulsorySubject: [
    "संस्कृतव्याकरणम्",
    "संस्कृतसाहित्यम्",
    "संस्कृतसामान्यम्",
    "हिंदी",
    "सामाजिकशिक्षा",
    "अंग्रेजी",
    "सामान्यविज्ञानम्",
  ],
  additionalSubject: [],
  address: {
    state: "Bihar",
    district: "",
    subDiv: "",
    po: "",
    ps: "",
    village: "",
    area: "",
    landmark: "",
    city: "",
    pincode: "",
  },
  nationality: "",
  aadhaar: "",
  country: "",
  foreignIdType: "",
  foreignIdNumber: "",
  handicapped: false,
  handicappedDetails: [],
  uploads: {
    colorPhoto: null,
    studentSignature: null,
    supportingDocument: null,
  },
};

const formSlice = createSlice({
  name: "form",
  initialState,
  reducers: {
    updateForm: (state, action) => {
      const mergeDeep = (target, source) => {
        Object.keys(source).forEach((key) => {
          if (
            source[key] &&
            typeof source[key] === "object" &&
            !Array.isArray(source[key]) &&
            !(source[key] instanceof File)
          ) {
            if (!target[key]) target[key] = {};
            mergeDeep(target[key], source[key]);
          } else {
            target[key] = source[key];
          }
        });
      };
      mergeDeep(state, action.payload);
    },
    resetForm: () => initialState,
  },
});

export const { updateForm, resetForm } = formSlice.actions;
export default formSlice.reducer;
