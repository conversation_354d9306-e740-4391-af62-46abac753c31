"use client";
import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import styles from "./styles.module.scss";
import { API_URL } from "@/config";

import { FileUpload } from "@/components/formSection/fileUpload";
import Label from "@/components/formSection/label"; 
import SelectForm from "@/components/formSection/selectForm";
import Handicap from "./handicap";
import validationSchema from "./validationSchema";
import StudentAddress from "./studentAddress";
import RegistrationPreview from "./preview";
import StudentCategory from "./studentCategory";
import StudentDetails from "./studentDetails";
import NationalitySection from "./nationality";
import PreviewTrigger from "../../../components/formSection/previewTrigger";
import { updateForm } from "@/features/form/formSlice";

import { toast } from "react-toastify";
import {
  useGetDistrictDropdownQuery,
  useGetSubdivisionDropdownQuery,
  useAddStudentRegistrationDetailsMutation,
} from "@/injectEndpoints/studentRegistrationendpoint";
import { useGetInstituteContactsQuery, useGetNodalSchoolIdQuery } from "@/injectEndpoints/institutedetailsendpoint";

const buildPayload = (formData, fileUrls, instituteDetails, schoolName, schoolCode, nodalSchoolId) => ({
  programCode: "ADDREGISTRATION",
  studentCategory: formData.studentCategory,
  oldRegistrationNumber: formData.oldRegNumber,
  registrationYear: formData.regYear,
  schoolCategory: String(instituteDetails?.code || formData.schoolCategory || ""),
  schoolName: schoolName || formData.schoolName,
  instituteCode: schoolCode || formData.schoolCode || "",
  district: instituteDetails?.district || formData.district || formData.address?.district,
  nodalSchoolId: nodalSchoolId || null,
  studentNameEng: formData.studentName?.english || "",
  studentNameHindi: formData.studentName?.hindi || "",
  fatherNameEng: formData.fatherName?.english || "",
  fatherNameHindi: formData.fatherName?.hindi || "",
  motherNameEng: formData.motherName?.english || "",
  motherNameHindi: formData.motherName?.hindi || "",
  dateOfBirth: formData.dob,
  gender: formData.gender,
  casteCategory: formData.caste,
  colorPhoto: fileUrls.colorPhoto || "",
  studentSignature: fileUrls.studentSignature || "",
  supportingDocumentUrl: fileUrls.supportingDocument || "",
  compulsorySubjects: formData.compulsorySubject,
  additionalSubject: formData.additionalSubject,
  studentAddressState: formData.address?.state,
  studentAddressDistrict: formData.address?.district,
  studentAddressSubDiv: formData.address?.subDiv,
  studentAddressPO: formData.address?.po,
  studentAddressPS: formData.address?.ps,
  studentAddressVillageMohalla: formData.address?.village,
  studentAddressArea: formData.address?.area,
  studentAddressLandmark: formData.address?.landmark,
  studentAddressCity: formData.address?.city,
  studentAddressPincode: formData.address?.pincode,
  studentMobileNo: formData.mobile,
  studentEmailId: formData.email,
  nationalityName: formData.nationality,
  aadharNo: formData.aadhaar,
  countryName: formData.country,
  countryIdType: formData.foreignIdType,
  documentNumber: formData.foreignIdNumber,
  isHandicapped: formData.handicapped,
  handicappedDetails: formData.handicapped === "Yes" ? formData.handicappedDetails : null,
});

const flattenErrors = (errors) => {
  const result = [];
  const recurse = (obj) => {
    Object.entries(obj).forEach(([key, value]) => {
      if (typeof value === "string") result.push(value);
      else if (typeof value === "object" && value !== null) recurse(value);
    });
  };
  recurse(errors);
  return result;
};

const SubdivisionFetcher = ({ district, children }) => {
  const { data: subdivisionData = [] } = useGetSubdivisionDropdownQuery(district, {
    skip: !district,
  });
  const options = ["Click here to select", ...subdivisionData];
  return children(options);
};

const RegistrationPage = () => {
  const [showPreview, setShowPreview] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const { instituteDetails } = useSelector((state) => state.auth);
  const { data: contactsData } = useGetInstituteContactsQuery();
  const contacts = contactsData || [];

  const schoolName =
    contacts.find((c) => c.contactType === "institute_name_eng")?.contactVype || "N/A";

  const schoolCode =
    contacts.find((c) => c.contactType === "institute_code")?.contactVype || "N/A";

  const nodalSchoolName =
    contacts.find((c) => c.contactType === "nodal_school")?.contactVype || "";

  const { data: nodalSchoolIdData } = useGetNodalSchoolIdQuery(nodalSchoolName, {
    skip: !nodalSchoolName,
  });
  const nodalSchoolId = nodalSchoolIdData?.[0]?.id || null;

  const router = useRouter();
  const dispatch = useDispatch();
  const savedForm = useSelector((state) => state.form);

  const { data: districtData = [] } = useGetDistrictDropdownQuery();
  const [addStudentRegistrationDetails] = useAddStudentRegistrationDetailsMutation();
  const districtOptions = ["Click here to select", ...districtData];

  useEffect(() => {

    if (localStorage.getItem("formSubmitted") === "true") {
      dispatch(updateForm({}));
      localStorage.removeItem("formSubmitted");
    }
    setMounted(true);
  }, [dispatch]);

  if (!mounted) return <p>Loading...</p>;

  const handlePaymentAndSubmit = async (values) => {
    try {
      const fileUrls = {
        colorPhoto: values.uploads?.colorPhoto || "",
        studentSignature: values.uploads?.studentSignature || "",
        supportingDocument: values.uploads?.supportingDocument || "",
      };

      const payload = buildPayload(
        values,
        fileUrls,
        instituteDetails,
        schoolName,
        schoolCode,
        nodalSchoolId
      );

      const res = await addStudentRegistrationDetails(payload).unwrap();

      if (!res?.data?.registrationId) {
        toast.error("Registration ID missing. Cannot proceed with payment.");
        return;
      }

      dispatch(updateForm({}));
      localStorage.setItem("formSubmitted", "true");

      const payUrl = `${API_URL}/payment/payment-initiate?registrationId=${res.data.registrationId}&productinfo=form`;
      window.location.href = payUrl;
    } catch (error) {
      console.error("Error in submit + payment:", error);

      if (error?.status === 409 && error?.data?.message) {
        toast.error(error.data.message);
      } else if (error?.data?.message) {
        toast.error(error.data.message);
      } else {
        toast.error("Submission or Payment failed. Please try again.");
      }
    }
  };

  const showNodalSchool = instituteDetails?.typeOfSchool === "प्रस्तावित";

  return (
    <div className={styles.registrationContainer}>
      <Formik
        initialValues={{
          ...savedForm,
          schoolCategory: instituteDetails?.code || savedForm.schoolCategory,
          schoolName: schoolName || savedForm.schoolName,
          schoolCode: schoolCode || savedForm.schoolCode,
          district: instituteDetails?.district || savedForm.district,
          nodalSchool: nodalSchoolName || savedForm.nodalSchool || "",
          uploads: savedForm.uploads || {
            colorPhoto: "",
            studentSignature: "",
            supportingDocument: "",
          },
        }}
        validationSchema={validationSchema}
        onSubmit={() => {}}
        enableReinitialize
      >
        {({ values, setFieldValue, validateForm }) => (
          <Form className={styles.formBox}>
            {!showPreview ? (
              <>
                <div className={styles.buttonRow}>
                  <div className={styles.halfButton}>
                    <button
                      type="button"
                      onClick={() =>
                        router.push("/instituteDashboard/registration/genralInstruction")
                      }
                    >
                      CLICK HERE TO VIEW THE GENERAL INSTRUCTION / सामान्य निर्देश
                    </button>
                  </div>
                  <div className={styles.halfButton}>
                    <button type="button" onClick={() => router.push("/instituteDashboard")}>
                      GO BACK TO DASHBOARD
                    </button>
                  </div>
                </div>

                <div className={styles.header}>
                  <h3>REGISTRATION-CUM-EXAMINATION FORM, MADHYAMA EXAM - 2026</h3>
                </div>

                <div className={styles.mainContent}>
                  <div className={styles.formSections}>
                    <div className={styles.twoColumnLayout}>
                      <Label label="1. STUDENT CATEGORY :" required />
                      <StudentCategory />
                    </div>

                    <div className={styles.section}>
                      <Label label="2. SCHOOL CATEGORY :" />
                      {instituteDetails?.code} कोटि
                    </div>

                    <div className={styles.section}>
                      <Label label="3. SCHOOL NAME :" />
                      <span>{schoolName}</span>
                    </div>

                    <div className={styles.section}>
                      <Label label="4. SCHOOL CODE :" />
                      <span>{schoolCode}</span>
                    </div>

                    <div className={styles.section}>
                      <Label label="5. DISTRICT :" />
                      {instituteDetails?.district}
                    </div>

                    {showNodalSchool && (
                      <div className={styles.section}>
                        <Label label=" NODAL SCHOOL :" required />
                        <span>{nodalSchoolName || "N/A"}</span>
                      </div>
                    )}
                  </div>

                  <div className={styles.uploadColumn}>
                    <FileUpload
                      type="photo"
                      label="Color Photo"
                      name="uploads.colorPhoto"
                      value={values.uploads.colorPhoto}
                      setFieldValue={setFieldValue}
                      accept="image/jpeg,image/png"
                    />
                    <FileUpload
                      label="Student Signature"
                      name="uploads.studentSignature"
                      value={values.uploads.studentSignature}
                      setFieldValue={setFieldValue}
                      accept="image/jpeg,image/png"
                    />
                  </div>
                </div>

                <div className={styles.fullWidthSection}>
                  <div className={styles.twoColumnLayout}>
                    <Label label="6. STUDENT INFO" />
                    <StudentDetails />
                  </div>

                  <div className={styles.twoColumnLayout}>
                    <Label label="7. COMPULSORY SUBJECTS :" required />
                    <SelectForm
                      type="checkbox"
                      name="compulsorySubject"
                      options={[
                        "संस्कृतव्याकरणम्",
                        "संस्कृतसाहित्यम्",
                        "संस्कृतसामान्यम्",
                        "हिंदी",
                        "सामाजिकशिक्षा",
                        "अंग्रेजी",
                        "सामान्यविज्ञानम्",
                      ]}
                      disabled
                    />
                  </div>

                  <div className={styles.twoColumnLayout}>
                    <Label label="8. ADDITIONAL SUBJECTS :" required />
                    <SelectForm
                      type="checkbox"
                      name="additionalSubject"
                      options={[
                        "गणित",
                        "अर्थशास्त्र",
                        "गृह विज्ञान",
                        "मैथिली",
                        "संगीत",
                        "पुरोहित्य",
                        "भोजपुरी",
                      ]}
                    />
                  </div>

                  <div className={styles.twoColumnLayout}>
                    <Label label="9. STUDENT ADDRESS" required />
                    <SubdivisionFetcher district={values?.address?.district}>
                      {(subdivisionOptions) => (
                        <StudentAddress
                          districtOptions={districtOptions}
                          subdivisionOptions={subdivisionOptions}
                        />
                      )}
                    </SubdivisionFetcher>
                  </div>

                  <div className={styles.twoColumnLayout}>
                    <Label label="10. NATIONALITY" required />
                    <NationalitySection />
                  </div>

                  <div className={styles.twoColumnLayout}>
                    <Label label="11. ARE YOU PHYSICALLY SPECIAL ABILITY ?" required />
                    <Handicap />
                  </div>
                </div>

                <div className={styles.termsCheckbox}>
                  <input
                    type="checkbox"
                    id="terms"
                    checked={termsAccepted}
                    onChange={(e) => setTermsAccepted(e.target.checked)}
                    style={{ marginRight: "8px" }}
                  />
                  <Label
                    label="मैं, इस संस्थान का प्रधानाचार्य, उपर्युक्त अंकित विवरण का मिलान विद्यालय अभिलेख से कर लिया हूँ जो सही है, परीक्षा आवेदन स्वीकार किया जा सकता है |"
                    htmlFor="terms"
                  />
                </div>

                <PreviewTrigger
                  dispatch={dispatch}
                  setShowPreview={setShowPreview}
                  termsAccepted={termsAccepted}
                  values={values}
                  validateForm={validateForm}
                  updateFormAction={updateForm}
                />
              </>
            ) : (
              <>
                <RegistrationPreview formData={savedForm} />
                <div className={styles.buttonRow}>
                  <div className={styles.halfButton}>
                    <button type="button" onClick={() => setShowPreview(false)}>
                      BACK TO EDIT
                    </button>
                  </div>
                  <div className={styles.halfButton}>
                    <button
                      type="button"
                      onClick={() => handlePaymentAndSubmit(savedForm)}
                    >
                      SUBMIT FINAL & PAY
                    </button>
                  </div>
                </div>
              </>
            )}
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default RegistrationPage;
