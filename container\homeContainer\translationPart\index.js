import React, { useState, useRef } from "react";
import classes from "./styles.module.scss";
import Image from "next/image";
import Select from "@/components/select";
import ClearRoundedIcon from '@mui/icons-material/ClearRounded';
import ContentCopyRoundedIcon from '@mui/icons-material/ContentCopyRounded';
import KeyboardOutlinedIcon from '@mui/icons-material/KeyboardOutlined';
import { apiFunction } from "@/apiCall/function";
import { TRANSLATE } from "@/apiCall/urls/apis";
import { loaderEnd, loaderStart, notification } from "@/components/loader";
import Button from "@/components/button";

const Translation = () => {

    const inputRef = useRef(null);
    const inputRef2 = useRef(null);

    const language = [
        { label: "English", value: "english" },
        { label: "Hindi", value: "hindi" },
    ];

    const language2 = [
        { label: "Sanskrit", value: "sanskrit" },
    ];

    const [data, setData] = useState("");
    const [res, setRes] = useState("");

    const translate = async () => {
        if (data) {
            loaderStart()
            let _data = {
                "text": data
            }
            let res = await apiFunction(TRANSLATE, "POST", _data);
            // console.log(res.data);
            if (res.status) {
                setRes(res.data.data.translatedText);
            }
            else {
                setRes("")
            }
            loaderEnd()
        }

    }

    const handleCopy = (inputRef) => {
        const input = inputRef.current;
        if (input) {
            navigator.clipboard.writeText(input.value)
                .then(() => notification(true, "Text copied"))
                .catch((err) => console.error('Copy failed', err));
        }
    };

    return (
        <div className={classes.translation}>
            <div className={classes.translation_content}>
                <p className={classes.header_text}>अनुवाद करें</p>
                <div className={classes.translation_section}>

                    <div className={classes.section}>
                        <div className={classes.section_head_filter}>
                            <p className={classes.head}>Select language</p>
                            <Select options={language} variant="v_one" />
                        </div>

                        <div className={classes.typing_area}>
                            <ClearRoundedIcon onClick={() => setData("")} className={classes.clear_field} />
                            <textarea  ref={inputRef} value={data} onChange={(e) => setData(e.target.value)} className={classes.typing} />
                            <div className={classes.btm_action}>
                                <ContentCopyRoundedIcon onClick={() => handleCopy(inputRef)} />
                                {/* <KeyboardOutlinedIcon /> */}
                            </div>
                        </div>
                    </div>

                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/switch.webp`} width={192} height={192} alt="icon" onClick={translate} className={classes.switch_icon} />

                    <div className={classes.section}>
                        <div className={classes.section_head_filter}>
                            <p className={classes.head}>Select language</p>
                            <Select options={language2} variant="v_one" />
                        </div>

                        <div className={classes.typing_area}>
                            {/* <ClearRoundedIcon className={classes.clear_field} /> */}
                            <textarea ref={inputRef2} value={res} className={classes.typing} />
                            <div className={classes.btm_action}>
                                <ContentCopyRoundedIcon onClick={() => handleCopy(inputRef2)} />
                                {/* <KeyboardOutlinedIcon /> */}
                            </div>
                        </div>
                    </div>
                    <Button 
                        button_text="अनुवाद करें"
                        variant="btn_brown"
                        second_class={classes.translate_btn}
                        onClick={translate}
                    />
                </div>
            </div>
        </div>
    )
}

export default Translation;