// Common styles for form and upload labels
.formLabel,
.uploadLabel {
  font-size: 1rem;
  font-weight: 500;
  color: var(--label-text-color, #571f0b);
  transition: font-size 0.3s ease, margin-bottom 0.3s ease;
  display: inline-flex; /* Ensure label and asterisk stay on the same line */
  align-items: center;  /* Vertically align label text and asterisk */
}

// Upload label specific styles
.uploadLabel {
  padding: 10px 20px;
  background-color: var(--button-bg-color, #571f0b);
  color: var(--button-text-color, #fff);
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease, padding 0.3s ease;
  border: 1px solid var(--button-border-color, #4a1a0a);
}

.uploadLabel:hover {
  background-color: var(--button-hover-bg-color, #45a049);
}

// Media Queries
@media (max-width: 768px) {
  .formLabel {
    font-size: 0.95rem;
    margin-bottom: 6px;
  }

  .uploadLabel {
    font-size: 0.95rem;
    padding: 8px 18px;
  }
}

@media (max-width: 480px) {
  .formLabel {
    font-size: 0.9rem;
    margin-bottom: 5px;
  }

  .uploadLabel {
    font-size: 0.9rem;
    padding: 7px 16px;
  }
}

// Prevent label text from wrapping
.labelText {
  white-space: nowrap;
}

// Asterisk style
.required {
  color: red;
  margin-left: 5px;
}
