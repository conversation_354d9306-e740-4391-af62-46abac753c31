import PropTypes from 'prop-types';
import { Suspense, useEffect, useState } from "react";
import Layout from "./layout";
import Script from 'next/script';
import Head from 'next/head';
import "../styles/global.css";
import { Provider } from 'react-redux';
import store from '../stores';
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SessionExpiredModal from '@/components/session-expired-modal';
import { usePathname } from 'next/navigation';

function MyApp({ Component, pageProps, ...rest }) {
    const [isReady, setIsReady] = useState(false);
    const pathname = usePathname();

    useEffect(() => {
        const interval = setInterval(() => {
            const frame = document.querySelector('.goog-te-banner-frame');
            if (frame) frame.style.display = 'none';

            const body = document.querySelector('body');
            if (body) body.style.top = '0px';
        }, 100);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        setIsReady(true);

        if (pathname === "/login" && localStorage.getItem("sessionExpired")) {
            localStorage.removeItem("sessionExpired");
        }
    }, [pathname]);

    return (
        <Provider store={store}>
            <Head>
                <title>Bihar Sanskrit Shiksha Board</title>
                <meta name="description" content="Bihar Sanskrit Shiksha Board" />
                <link rel='icon' href='/favicon.ico' type="image/x-icon" />
                <script async src="https://instagram.com/static/bundles/es6/EmbedSDK.js/47c7ec92d91e.js"></script>
            </Head>
            <Script
                strategy="lazyOnload"
                src="https://www.googletagmanager.com/gtag/js?id=G-CYZC7Z8D2E"
            />
            <Layout>
                <Suspense fallback={<div>Loading...</div>}>
                    <Component {...pageProps} />
                </Suspense>
            </Layout>
            <ToastContainer position="top-center" autoClose={1500} />
            {isReady && pathname !== "/login" && <SessionExpiredModal />}
        </Provider>
    );
}

MyApp.propTypes = {
    Component: PropTypes.elementType.isRequired,
    pageProps: PropTypes.object.isRequired,
};

export default MyApp;
