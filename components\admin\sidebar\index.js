"use client";
import Link from "next/link";
import { useRouter } from "next/router";
import styles from "./styles.module.scss";
import {
  FaCrown,
  FaHome,
  FaBook,
  FaUniversity,
  FaGraduationCap,
  FaLink,
  FaImages,
  FaBookReader,
  FaUserCircle,
  FaChevronRight,
} from "react-icons/fa";

export default function Sidebar() {
  const router = useRouter();

  const isActive = (path) => {
    return router.pathname === path;
  };

  return (
    <aside className={styles.sidebar}>
      <div className={styles.logoRow}>
        <FaCrown className={styles.crownIcon} />

        <h2 className={styles.logo}> Dashboard</h2>
      </div>
      <hr className={styles.divider} />

      <button className={styles.profileBtn}>
        <FaUserCircle className={styles.profileIcon} />
        <span>Admin Profile</span>
        <FaChevronRight className={styles.arrow} />
      </button>

      <nav className={styles.nav}>
        <span className={styles.sectionLabel}>Manage</span>
        <ul className={styles.navList}>
          <li
            className={`${styles.navItem} ${
              isActive("/admin") ? styles.active : ""
            }`}
          >
            <Link href="/admin" className={styles.navLink}>
              <FaHome className={styles.navIcon} />
              <span>Homepage</span>
              <FaChevronRight className={styles.arrow} />
            </Link>
          </li>
          <li
            className={`${styles.navItem} ${
              isActive("/admin/policies") ? styles.active : ""
            }`}
          >
            <Link href="/admin/policies" className={styles.navLink}>
              <FaBook className={styles.navIcon} />
              <span>Policies</span>
              <FaChevronRight className={styles.arrow} />
            </Link>
          </li>
          <li
            className={`${styles.navItem} ${
              isActive("/admin/academics") ? styles.active : ""
            }`}
          >
            <Link href="/admin/academics" className={styles.navLink}>
              <FaGraduationCap className={styles.navIcon} />
              <span>Registration Reports</span>
              <FaChevronRight className={styles.arrow} />
            </Link>
          </li>
          <li
            className={`${styles.navItem} ${
              isActive("/admin/Institutions") ? styles.active : ""
            }`}
          >
            <Link href="/admin/Institutions" className={styles.navLink}>
              <FaUniversity className={styles.navIcon} />
              <span>Institutions Reports</span>
              <FaChevronRight className={styles.arrow} />
            </Link>
          </li>

          <li
            className={`${styles.navItem} ${
              isActive("/admin/adminLibrary") ? styles.active : ""
            }`}
          >
            <Link href="/admin/adminLibrary" className={styles.navLink}>
              <FaBookReader className={styles.navIcon} />
              <span>Digital Library</span>
              <FaChevronRight className={styles.arrow} />
            </Link>
          </li>
          <li
            className={`${styles.navItem} ${
              isActive("/admin/gallery") ? styles.active : ""
            }`}
          >
            <Link href="/admin/gallery" className={styles.navLink}>
              <FaImages className={styles.navIcon} />
              <span>Gallery</span>
              <FaChevronRight className={styles.arrow} />
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  );
}
