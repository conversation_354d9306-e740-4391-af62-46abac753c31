"use client";
import React from "react";
import PropTypes from "prop-types";
import { useFormikContext } from "formik";
import styles from "../styles.module.scss";
import Label from "@/components/formSection/label";
import Input from "@/components/formSection/input";
import SelectForm from "@/components/formSection/selectForm";

// Notification function (for demo purposes, you can replace with toast notifications)
const notification = (isSuccess, message) => {
  alert(message); // Simple alert for now
};

const StudentAddress = ({ districtOptions = [], subdivisionOptions = [] }) => {
  const { values, setFieldValue } = useFormikContext();

  const biharDropdowns = {
    district: districtOptions.length > 0 ? districtOptions : [],
    subdivision: subdivisionOptions.length > 0 ? subdivisionOptions : [],
  };

  const handleStateToggle = (state) => {
    setFieldValue("address.state", state);
    setFieldValue("address.district", "");
    setFieldValue("address.subDiv", "");
  };

  return (
    <div className={styles.twoColumnLayout}>
      <div>
        {/* State Toggle */}
        <div className={styles.radioGroup}>
          {["Bihar", "Other"].map((option) => (
            <label key={option} className={styles.radioOption}>
              <input
                type="radio"
                name="address.stateToggle"
                value={option}
                checked={values.address.state === option}
                onChange={() => handleStateToggle(option)}
              />
              {option}
            </label>
          ))}
        </div>

        {/* Address Fields */}
        <div className={styles.addressGrid}>
          {values.address.state === "Other" && (
            <div className={styles.inputContainer}>
              <Label label="STATE :" htmlFor="address.state" required />
              <Input
                name="address.state"
                placeholder="Enter State"
                value={values.address.state}
                onChange={(e) => setFieldValue("address.state", e.target.value)}
              />
            </div>
          )}

          {/* District */}
          <div className={styles.inputContainer}>
            <Label label="DISTRICT :" htmlFor="address.district" required />
            {values.address.state === "Bihar" ? (
              <SelectForm
                type="select"
                name="address.district"
                options={biharDropdowns.district}
                onChange={(e) => setFieldValue("address.district", e.target.value)}
              />
            ) : (
              <Input
                name="address.district"
                placeholder="Enter District"
                value={values.address.district}
                onChange={(e) => setFieldValue("address.district", e.target.value)}
              />
            )}
          </div>

          {/* Sub-Div */}
          <div className={styles.inputContainer}>
            <Label label="SUB-DIV :" htmlFor="address.subDiv" required />
            {values.address.state === "Bihar" ? (
              <SelectForm
                type="select"
                name="address.subDiv"
                options={biharDropdowns.subdivision}
                onChange={(e) => setFieldValue("address.subDiv", e.target.value)}
              />
            ) : (
              <Input
                name="address.subDiv"
                placeholder="Enter Sub-Division"
                value={values.address.subDiv}
                onChange={(e) => setFieldValue("address.subDiv", e.target.value)}
              />
            )}
          </div>

          {/* Area */}
          <div className={styles.inputContainer}>
            <Label label="AREA / STREET / SECTOR :" htmlFor="address.area" />
            <Input
              name="address.area"
              placeholder="Enter Area / Street / Sector"
              value={values.address.area}
              onChange={(e) => setFieldValue("address.area", e.target.value)}
            />
          </div>

          {/* Village */}
          <div className={styles.inputContainer}>
            <Label label="VILLAGE / MOHALLA :" htmlFor="address.village" required />
            <Input
              name="address.village"
              placeholder="Enter Village/Mohalla"
              value={values.address.village}
              onChange={(e) => setFieldValue("address.village", e.target.value)}
            />
          </div>

          {/* City */}
          <div className={styles.inputContainer}>
            <Label label="TOWN / CITY :" htmlFor="address.city" required />
            <Input
              name="address.city"
              placeholder="Enter Town / City"
              value={values.address.city}
              onChange={(e) => setFieldValue("address.city", e.target.value)}
            />
          </div>

          {/* Police Station */}
          <div className={styles.inputContainer}>
            <Label label="POLICE STATION :" htmlFor="address.ps" />
            <Input
              name="address.ps"
              placeholder="Enter Police Station"
              value={values.address.ps}
              onChange={(e) => setFieldValue("address.ps", e.target.value)}
            />
          </div>

          {/* Landmark */}
          <div className={styles.inputContainer}>
            <Label label="LANDMARK :" htmlFor="address.landmark" />
            <Input
              name="address.landmark"
              placeholder="Enter Landmark"
              value={values.address.landmark}
              onChange={(e) => setFieldValue("address.landmark", e.target.value)}
            />
          </div>

          {/* Post Office */}
          <div className={styles.inputContainer}>
            <Label label="POST OFFICE :" htmlFor="address.po" />
            <Input
              name="address.po"
              placeholder="Enter Post Office"
              value={values.address.po}
              onChange={(e) => setFieldValue("address.po", e.target.value)}
            />
          </div>

          {/* Pincode */}
          <div className={styles.inputContainer}>
            <Label label="PINCODE :" htmlFor="address.pincode" required />
            <Input
              name="address.pincode"
              placeholder="Enter Pincode"
              value={values.address.pincode}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, ""); // Allow only numeric input
                if (value.length <= 6) {
                  setFieldValue("address.pincode", value);
                } else {
                  notification(false, "Pincode must be 6 digits.");
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

StudentAddress.propTypes = {
  districtOptions: PropTypes.arrayOf(PropTypes.string),
  subdivisionOptions: PropTypes.arrayOf(PropTypes.string),
};

export default StudentAddress;
