'use client';
import PropTypes from 'prop-types';
import classes from "./styles.module.scss";
import Header from "../container/header";
import Footer from '../container/footer';

const Layout = ({ children }) => {

    return (
        <div className={classes.main_page_container} id="mainBody">
            <div className={classes.header}>
                <Header />
            </div>
            <div className={classes.content}>
                {children}
            </div>
            <div className={classes.footer}>
                <Footer />
            </div>
        </div>
    );
};

Layout.propTypes = {
    children: PropTypes.node,
};

export default Layout;