"use client";
import React, { useState } from "react";
import styles from "./styles.module.scss";
import { FiUpload } from "react-icons/fi";
import AdminLibrary from "@/components/admin/adminLibrary";
import Pagination from "@/components/admin/pagination";

export default function AdminLibraryContainer() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;
  const openFileupload = () => setModalVisible(true);

  // Dummy data for now
  const sections = [
    {
      books: [
        {
          id: 1,
          title: "Ramcharit Manas",
          readUrl: "https://example.com/books/ramcharitmanas.pdf",
        },
        {
          id: 2,
          title: "Srimad Bhagavad Gita",
          readUrl: "https://example.com/books/bhagavadgita.pdf",
        },
        {
          id: 3,
          title: "Class 1",
          gradeClass: 1,
        },
        {
          id: 4,
          title: "Class 2",
          gradeClass: 2,
        },
        {
          id: 5,
          title: "Class 3",
          gradeClass: 3,
        },
        {
          id: 6,
          title: "Class 4",
          gradeClass: 4,
        },
        {
          id: 7,
          title: "Class 5",
          gradeClass: 5,
        },
        {
          id: 8,
          title: "Class 6",
          gradeClass: 6,
        },
        {
          id: 9,
          title: "Class 7",
          gradeClass: 7,
        },
        {
          id: 10,
          title: "Class 8",
          gradeClass: 8,
        },
        {
          id: 11,
          title: "Class 9",
          gradeClass: 9,
        },
        {
          id: 12,
          title: "Class 10",
          gradeClass: 10,
        },
      ],
    },
  ];

  // Get all books from sections
  const allBooks = sections[0]?.books || [];
  const totalItems = allBooks.length;

  // Calculate pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentBooks = allBooks.slice(startIndex, startIndex + itemsPerPage);

  const paginatedSections = [
    {
      books: currentBooks,
    },
  ];

  return (
    <div className={styles.notifications}>
      <div className={styles.header}>
        <h3 className={styles.title}>View/Upload Books:</h3>
        <button className={styles.addBtn} onClick={openFileupload}>
          <FiUpload /> &nbsp; Upload New
        </button>
      </div>

      <div className={styles.content}>
        <AdminLibrary
          sections={paginatedSections}
          cdnBase={process.env.NEXT_PUBLIC_CDN_URL}
          onOpenBook={(book) => {
            if (book.gradeClass) {
              alert(`Open Class ${book.gradeClass} books page`);
            }
          }}
        />
      </div>

      <div className={styles.paginationWrapper}>
        <Pagination
          totalItems={totalItems}
          itemsPerPage={itemsPerPage}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
        />
      </div>
    </div>
  );
}
