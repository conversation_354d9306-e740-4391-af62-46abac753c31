import { apiSlice } from "@/apiSlice/apiSlice";
import { adminendpoints } from "@/constants/endpoints";

export const adminApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    //FOR NOTIFICATION
    getNotifications: builder.query({
      query: (data) => ({
        url: adminendpoints.listNotification,
        method: "POST",
        body: data,
      }),
      // 'providesTags' is important for automatic re-fetching after a mutation
      providesTags: ["Notification"],
    }),
    addNotification: builder.mutation({
      query: (data) => ({
        url: adminendpoints.addNotification,
        method: "POST",
        body: data,
      }),
      // Invalidate the cache to trigger a re-fetch of the list
      invalidatesTags: ["Notification"],
    }),
    deleteNotification: builder.mutation({
      query: (data) => ({
        url: adminendpoints.deleteNotification,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Notification"],
    }),
    // FOR POLICIES
    getPolicies: builder.query({
      query: (data) => ({
        url: adminendpoints.listPolicy,
        method: "POST",
        body: data,
      }),
      providesTags: ["Policy"],
    }),
    addPolicy: builder.mutation({
      query: (data) => ({
        url: adminendpoints.addPolicy,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Policy"],
    }),
    deletePolicy: builder.mutation({
      query: (data) => ({
        url: adminendpoints.deletePolicy,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Policy"],
    }),
    // FOR LIBRARY
    getLibrary: builder.query({
      query: (data) => ({
        url: adminendpoints.listLibrary,
        method: "POST",
        body: data,
      }),
      providesTags: ["Library"],
    }),
    addLibrary: builder.mutation({
      query: (data) => ({
        url: adminendpoints.addLibrary,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Library"],
    }),
    deleteLibrary: builder.mutation({
      query: (data) => ({
        url: adminendpoints.deleteLibrary,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Library"],
    }),
    // FOR GALLERY
    getGallery: builder.query({
      query: (data) => ({
        url: adminendpoints.listGallery,
        method: "POST",
        body: data,
      }),
      providesTags: ["Gallery"],
    }),
    addGallery: builder.mutation({
      query: (data) => ({
        url: adminendpoints.addGallery,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Gallery"],
    }),
    deleteGallery: builder.mutation({
      query: (data) => ({
        url: adminendpoints.deleteGallery,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Gallery"],
    }),
  }),
});

export const {
  useGetNotificationsQuery,
  useAddNotificationMutation,
  useDeleteNotificationMutation,
  useGetPoliciesQuery,
  useAddPolicyMutation,
  useDeletePolicyMutation,
  useGetGalleryQuery,
  useAddGalleryMutation,
  useDeleteGalleryMutation,
} = adminApiSlice;
