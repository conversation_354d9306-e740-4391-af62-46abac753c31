@import "../../../assets/css/global.scss";

.gallery_remove_card {
  background: $white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: 220px;
  height: auto;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .image_container {
    width: 100%;
    height: 150px;
    overflow: hidden;
    position: relative;
    border: 1px solid $borderOne;
    border-radius: 12px 12px 0 0;

    .card_image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
      border-radius: 11px 11px 0 0;
    }
  }

  .card_content {
    padding: 10px;
    flex-grow: 1;
    background: $white;
    border-radius: 0 0 12px 12px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .card_title {
      font-family: $poppinsSemiBold600;
      font-size: 11px;
      color: $textBlackTwo;
      margin: 0;
      line-height: 1;
    }

    .card_date {
      font-family: $poppinsRegular400;
      font-size: 11px;
      color: $textBlack;
      margin: 0;
      opacity: 0.8;
    }

    .remove_button {
      background: $darkBrown;
      color: $white;
      border: none;
      padding: 7px 7px;
      margin-top: 2px;
      font-family: $poppinsMedium500;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      width: 100%;
      border-radius: 6px;

      &:hover {
        background: $reddishBrown;
      }

      &:active {
        background: $burntCoffee;
      }

      &:focus {
        outline: 2px solid $darkBrown;
        outline-offset: 2px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .gallery_remove_card {
    width: 100%;
    max-width: 320px;
  }
}
