"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import classes from "./styles.module.scss";
import Input from "@/components/input";
import Button from "@/components/button";
import { useDispatch, useSelector } from "react-redux";
import { useLoginMutation } from "@/injectEndpoints/authendpoint";
import { setCredentials } from "@/features/auth/authSlice";
import Cookies from "js-cookie";

const LoginContainer = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab");

  const dispatch = useDispatch();
  const { token, user } = useSelector((state) => state.auth);
  const [login, { isLoading }] = useLoginMutation();

  // =============================
  // Local States
  // =============================
  const [activeTab, setActiveTab] = useState(tab === "admin" ? "admin" : "institute");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [errorMsg, setErrorMsg] = useState("");

  // Forgot Password Flow States
  const [showInstituteLogin, setShowInstituteLogin] = useState(true);
  const [showInstituteFgtPwd, setShowInstituteFgtPwd] = useState(false);
  const [showInstituteOTP, setShowInstituteOTP] = useState(false);
  const [showInstituteNewPwd, setShowInstituteNewPwd] = useState(false);

  const [showAdminLogin, setShowAdminLogin] = useState(true);
  const [showAdminFgtPwd, setShowAdminFgtPwd] = useState(false);
  const [showAdminOTP, setShowAdminOTP] = useState(false);
  const [showAdminNewPwd, setShowAdminNewPwd] = useState(false);

  // =============================
  // Clear Auth if not logged in
  // =============================
  useEffect(() => {
    if (!token) {
      Cookies.remove("token");
      Cookies.remove("user_role");

      dispatch(setCredentials({ token: null, user: null }));
      localStorage.removeItem("token");
      localStorage.removeItem("user_role");
      sessionStorage.removeItem("token");
      sessionStorage.removeItem("user_role");
    }
  }, [token, dispatch]);

  // =============================
  // Redirect if token exists
  // =============================
  useEffect(() => {
    if (token) {
      const userRole = Array.isArray(user)
        ? user?.[0]?.userRole
        : user?.userRole || "institute";

      if (userRole === "admin") router.replace("/admin");
      else router.replace("/instituteDashboard");
    }
  }, [token, user, router]);

  // =============================
  // Tab handling based on query param
  // =============================
  useEffect(() => {
    if (tab === "admin") handleTabClick("admin");
    else handleTabClick("institute");
  }, [tab]);

  // =============================
  // Handle Tab Click
  // =============================
  const handleTabClick = (tab) => {
    setActiveTab(tab);
    setErrorMsg("");
    setUsername("");
    setPassword("");

    // Reset Institute states
    setShowInstituteLogin(tab === "institute");
    setShowInstituteFgtPwd(false);
    setShowInstituteOTP(false);
    setShowInstituteNewPwd(false);

    // Reset Admin states
    setShowAdminLogin(tab === "admin");
    setShowAdminFgtPwd(false);
    setShowAdminOTP(false);
    setShowAdminNewPwd(false);
  };

  // =============================
  // Handle Login
  // =============================
  // =============================
  // Handle Login
  // =============================
  const handleLogin = async (e) => {
    e.preventDefault();
    if (!username || !password) {
      setErrorMsg("Please enter username and password.");
      return;
    }
    try {
      const payload = { login: username, password, user_role: activeTab };
      const response = await login(payload).unwrap();

      dispatch(setCredentials(response));

      const userRole = Array.isArray(response.user)
        ? response.user?.[0]?.userRole
        : response.user?.userRole || activeTab;

      Cookies.set("token", response.token, { expires: 1 });
      Cookies.set("user_role", userRole, { expires: 1 });

      if (userRole === "institute") router.push("/instituteDashboard");
      else router.push("/admin");
    } catch (err) {
      console.error("Login failed:", err);
      let message = "Login failed. Please try again.";
      if (err?.data?.message && typeof err.data.message === "string")
        message = err.data.message;
      else if (err?.error && typeof err.error === "string")
        message = err.error;
      setErrorMsg(message);
    }
  };

  // =============================
  // Forgot Password Flow Handlers
  // =============================
  const handleShowFgtPwd = (role) => {
    if (role === "institute") {
      setShowInstituteLogin(false);
      setShowInstituteFgtPwd(true);
    } else {
      setShowAdminLogin(false);
      setShowAdminFgtPwd(true);
    }
  };

  const handleShowOTP = (role) => {
    if (role === "institute") {
      setShowInstituteFgtPwd(false);
      setShowInstituteOTP(true);
    } else {
      setShowAdminFgtPwd(false);
      setShowAdminOTP(true);
    }
  };

  const handleShowNewPwd = (role) => {
    if (role === "institute") {
      setShowInstituteOTP(false);
      setShowInstituteNewPwd(true);
    } else {
      setShowAdminOTP(false);
      setShowAdminNewPwd(true);
    }
  };

  const handleSaveNewPwd = (role) => {
    if (role === "institute") {
      setShowInstituteLogin(true);
      setShowInstituteNewPwd(false);
    } else {
      setShowAdminLogin(true);
      setShowAdminNewPwd(false);
    }
  };

  // =============================
  // Render UI
  // =============================
  return (
    <div className={classes.login_container}>
      <div className={classes.login_content}>
        <p className={classes.login_header}>Welcome Back!</p>
        <p className={classes.sub_header}>
          Let’s take another step towards knowledge, tradition, and culture
        </p>

        <div className={classes.login_form}>
          {/* TAB HEADER */}
          <div className={classes.form_header}>
            <button
              onClick={() => handleTabClick("institute")}
              className={`${classes.tab_btn} ${activeTab === "institute" ? classes.active_tab : ""}`}
            >
              Institute Login
            </button>
            <button
              onClick={() => handleTabClick("admin")}
              className={`${classes.tab_btn} ${activeTab === "admin" ? classes.active_tab : ""}`}
            >
              Admin Login
            </button>
          </div>

          <div className={classes.form_body}>
            <p className={classes.label}>
              Enter your username and password to access your account
            </p>

            {/* ================= INSTITUTE LOGIN ================= */}
            {activeTab === "institute" && (
              <>
                {showInstituteLogin && (
                  <form className={classes.form} onSubmit={handleLogin}>
                    <Input
                      type="text"
                      placeholder="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                    />
                    <Input
                      type="password"
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />

                    <div className={classes.remember_me}>
                      <label className={classes.remember_checkbox}>
                        <input type="checkbox" /> Remember Me
                      </label>
                      <button
                        type="button"
                        className={classes.forget_password_btn}
                        onClick={() => handleShowFgtPwd("institute")}
                      >
                        Forgot Password?
                      </button>
                    </div>

                    {errorMsg && <p style={{ color: "red" }}>{errorMsg}</p>}

                    <Button
                      button_text={isLoading ? "Logging in..." : "Log In"}
                      variant="btn_reddishBrown"
                      type="submit"
                      disabled={isLoading}
                    />
                  </form>
                )}

                {showInstituteFgtPwd && (
                  <form className={classes.form}>
                    <Input type="email" placeholder="Enter registered Email Id" />
                    <Button
                      button_text="Send OTP"
                      variant="btn_reddishBrown"
                      onClick={() => handleShowOTP("institute")}
                    />
                  </form>
                )}

                {showInstituteOTP && (
                  <form className={classes.form}>
                    <Input type="number" placeholder="Enter OTP" />
                    <Button
                      button_text="Submit OTP"
                      variant="btn_reddishBrown"
                      onClick={() => handleShowNewPwd("institute")}
                    />
                  </form>
                )}

                {showInstituteNewPwd && (
                  <form className={classes.form}>
                    <Input type="password" placeholder="Enter new password" />
                    <Input type="password" placeholder="Re-enter new password" />
                    <Button
                      button_text="Save"
                      variant="btn_reddishBrown"
                      onClick={() => handleSaveNewPwd("institute")}
                    />
                  </form>
                )}
              </>
            )}

            {/* ================= ADMIN LOGIN ================= */}
            {activeTab === "admin" && (
              <>
                {showAdminLogin && (
                  <form className={classes.form} onSubmit={handleLogin}>
                    <Input
                      type="text"
                      placeholder="Username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                    />
                    <Input
                      type="password"
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />

                    <div className={classes.remember_me}>
                      <label className={classes.remember_checkbox}>
                        <input type="checkbox" /> Remember Me
                      </label>
                      <button
                        type="button"
                        className={classes.forget_password_btn}
                        onClick={() => handleShowFgtPwd("admin")}
                      >
                        Forgot Password?
                      </button>
                    </div>

                    {errorMsg && <p style={{ color: "red" }}>{errorMsg}</p>}

                    <Button
                      button_text={isLoading ? "Logging in..." : "Log In"}
                      variant="btn_reddishBrown"
                      type="submit"
                      disabled={isLoading}
                    />
                  </form>
                )}

                {showAdminFgtPwd && (
                  <form className={classes.form}>
                    <Input type="email" placeholder="Enter registered Email Id" />
                    <Button
                      button_text="Send OTP"
                      variant="btn_reddishBrown"
                      onClick={() => handleShowOTP("admin")}
                    />
                  </form>
                )}

                {showAdminOTP && (
                  <form className={classes.form}>
                    <Input type="number" placeholder="Enter OTP" />
                    <Button
                      button_text="Submit OTP"
                      variant="btn_reddishBrown"
                      onClick={() => handleShowNewPwd("admin")}
                    />
                  </form>
                )}

                {showAdminNewPwd && (
                  <form className={classes.form}>
                    <Input type="password" placeholder="Enter new password" />
                    <Input type="password" placeholder="Re-enter new password" />
                    <Button
                      button_text="Save"
                      variant="btn_reddishBrown"
                      onClick={() => handleSaveNewPwd("admin")}
                    />
                  </form>
                )}
              </>
            )}

            <div className={classes.terms_conditions}>
              <p className={classes.terms_conditions_text}>
                By logging in, you agree to our Terms of Services and Privacy Policy.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginContainer;
