// app/api/login/route.js
import { NextResponse } from "next/server";

export async function POST(req) {
  try {
    const body = await req.json();

    // ✅ call your real backend API
    const response = await fetch(`${process.env.API_URL}/login`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: data?.message || "Invalid credentials" },
        { status: 401 }
      );
    }

    const res = NextResponse.json(data);

    // ✅ Set HttpOnly cookies (middleware can read these)
    res.cookies.set("token", data.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      path: "/",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });
    res.cookies.set("role", data.user?.[0]?.userRole || "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      path: "/",
      maxAge: 60 * 60 * 24 * 7,
    });

    return res;
  } catch (err) {
    console.error("API Login Error:", err);
    return NextResponse.json({ error: "Something went wrong" }, { status: 500 });
  }
}
