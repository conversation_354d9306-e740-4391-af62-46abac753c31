import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '@/congif';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState()?.auth?.token;
      const language = getState()?.auth?.language;

      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      if (language) {
        headers.set('Accept-Language', language);
      }

      return headers;
    },
  }),
  endpoints: () => ({}),
});
