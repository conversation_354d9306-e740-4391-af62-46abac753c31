import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";

const Footer = () => {
    return(
        <div className={classes.footer_container}>
            <div className={classes.footer_content}>

                <div className={classes.top_footer}>
                    <div className={classes.logo_address}>
                        <div className={classes.logos}>
                            <Image 
                                src={`${process.env.NEXT_PUBLIC_CDN_URL}images/saraswatiMata2.webp`}
                                width={180} height={181}
                                alt="logo"
                            />
                            <Image 
                                src={`${process.env.NEXT_PUBLIC_CDN_URL}images/bSLogo2.webp`}
                                width={183} height={183}
                                alt="logo"
                            />
                        </div>
                        <div className={classes.address_sec}>
                            <p className={classes.hindi_heading}>बिहार संस्कृत शिक्षा बोर्ड</p>
                            <p className={classes.english_heading}>Bihar Sanskrit Shiksha Board</p>
                            <p className={classes.address}>17, बैक होर्डिंग रोड, पटना-800001</p>
                        </div>
                    </div>

                    <div className={classes.footer_nav_links}>
                        <p className={classes.links_header}>Quick Access</p>
                        <nav className={classes.links}>
                            <Link href={"/"}>Home</Link>
                            <Link href={"/about-us"}>About Us</Link>
                            <Link href={"/institutions"}>Institutions</Link>
                        </nav>
                    </div>

                    <div className={classes.footer_nav_links}>
                        <p className={classes.links_header} style={{visibility: "hidden"}}>Quick Access</p>
                        <nav className={classes.links}>
                            <Link href={"/digital-library"}>Digital Library</Link>
                            <Link href={"/gallery"}>Gallery</Link>
                        </nav>
                    </div>

                    <div className={classes.footer_nav_links}>
                        <p className={classes.links_header}>Contact Us</p>
                        <nav className={classes.links}>
                            <a href="tel:+919472269757" className={classes.a}>
                                <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}icons/call.svg`} width={10} height={10} alt="icon" /> +91 9472269757
                            </a>
                            <a href="tel:+916122217880" className={classes.a}>
                                <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}icons/call.svg`} width={10} height={10} alt="icon" /> 0612-2217880
                            </a>
                            <a href="mailto:<EMAIL>" className={classes.a}>
                                <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}icons/email.svg`} width={10} height={10} alt="icon" /> <EMAIL>
                            </a>
                            <a href="https://www.google.com/maps/dir//17,Back,+Harding+Rd,+Bihar+800014/@25.5911831,85.0207462,32537m/data=!3m1!1e3!4m8!4m7!1m0!1m5!1m1!1s0x39ed57f9b8557dbd:0x3f5e9140f713c496!2m2!1d85.1031477!2d25.5912062?entry=ttu&g_ep=EgoyMDI1MDgwNC4wIKXMDSoASAFQAw%3D%3D" className={classes.a}>
                                <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}icons/location2.svg`} width={10} height={10} alt="icon" /> 17,Back, Harding Rd Patna, 800001, Bihar
                            </a>
                            <div className={classes.social_icons}>
                                <a href="https://www.instagram.com/bssb.patna" target="_blank"><Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/instagram.svg`} width={20} height={20} alt="socials" className={classes.s_icon} /></a>
                                <a href="https://www.facebook.com/bssbpatna/" target="_blank"><Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/facebook.svg`} width={20} height={20} alt="socials" className={classes.s_icon} /></a>
                                <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/twitter.svg`} width={20} height={20} alt="socials" className={classes.s_icon} />
                            </div>
                        </nav>
                    </div>
                    {/* <div className={classes.footer_nav_links}>
                        <p className={classes.links_header}>Let Us Help You</p>
                        <nav className={classes.links}>
                            <Link href={"/"}>FAQs</Link>
                            <Link href={"/"}>Helpline No.</Link>
                            <Link href={"/"}>Manage Account</Link>
                            <Link href={"/"}>Forgot Password</Link>
                            <Link href={"/"}>RTI Information</Link>
                        </nav>
                    </div> */}
                </div>

                <div className={classes.bottom_footer}>
                    <p className={classes.left_text}>Design & Developed by Codebucket Solutions</p>

                    <p className={classes.left_text}>
                        © {new Date().getFullYear()} Bihar Sanskrit Shiksha Board. All rights reserved.
                    </p>
                    
                    <div className={classes.privacy_terms_services}>
                        <Link href={""}>Privacy Policy</Link>
                        <Link href={""}>Terms of Service</Link>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Footer;