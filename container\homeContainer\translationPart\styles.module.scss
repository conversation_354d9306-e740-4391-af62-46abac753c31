@import "../../../assets/css/global.scss";

.translation{
    width: 100%;
    height: auto;
    padding: 80px 50px;
    background-color: #571F0B;

    .translation_content{
        width: 100%;
        height: auto;

        .header_text{
            color: white;
            font-size: 28px;
            font-family: $poppinsBold700;
            text-align: center;
        }

        .translation_section{
            width: 100%;
            height: auto;
            padding: 40px 40px 80px;
            border-radius: 12px;
            background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/images/bgOldPaper.webp');
            background-repeat: no-repeat;
            background-size: 100% auto;
            margin-top: 20px;
            display: flex;
            align-items: center;
            position: relative;

            .section{
                width: 50%;
                height: auto;
                padding: 19px 28px;
                border-radius: 12px;
                border: 1px solid #571F0B;

                .section_head_filter{
                    width: 100%;
                    height: auto;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 11px 24px;
                    background-color: #571F0B;
                    border-radius: 12px;
                    margin-bottom: 25px;

                    .head{
                        color: white;
                        font-size: 16px;
                        font-family: $poppinsMedium500;
                    }
                }

                .typing_area{
                    position: relative;

                    .clear_field{
                        position: absolute;
                        right: 14px;
                        top: 14px;
                        color: #571F0B;
                        font-size: 25px;
                    }

                    .typing{
                        width: 100%;
                        height: 200px;
                        resize: none;
                        border: 1px solid #571F0B;
                        outline: unset;
                        border-radius: 12px;
                        background-color: transparent;
                        color: #571f0bcc;
                        font-size: 16px;
                        font-family: $poppinsMedium500;
                        padding: 24px;
                    }

                    .btm_action{
                        margin-top: 20px;
                        width: 100%;
                        height: auto;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        
                        svg{
                            font-size: 24px;
                            cursor: pointer;
                            transition: 200ms;

                            &:hover{
                                scale: 1.05;
                                filter: drop-shadow(0px 0px 4px #571F0B);
                            }
                        }
                    }
                }
            }

            .switch_icon{
                width: 48px;
                height: auto;
                margin: 0 -10px;
            }

            .translate_btn{
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: 20px;
            }
        }
    }
}