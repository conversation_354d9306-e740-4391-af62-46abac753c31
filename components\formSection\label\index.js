import React from "react";
import PropTypes from "prop-types";
import styles from "./styles.module.scss";

const Label = ({ label, htmlFor, className, isUpload = false, required = false }) => {
  const labelClass = isUpload ? styles.uploadLabel : styles.formLabel;

  return (
    <label htmlFor={htmlFor} className={`${labelClass} ${className}`}>
      <span className={styles.labelText}>{label}</span>
      {required && <span className={styles.required}>*</span>}
    </label>
  );
};

Label.propTypes = {
  label: PropTypes.string.isRequired,
  htmlFor: PropTypes.string,
  className: PropTypes.string,
  isUpload: PropTypes.bool,
  required: PropTypes.bool,
};

Label.defaultProps = {
  htmlFor: "",
  className: "",
  isUpload: false,
  required: false,
};

export default Label;
