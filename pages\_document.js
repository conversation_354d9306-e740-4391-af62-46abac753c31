import { Html, <PERSON>, <PERSON>, NextScript } from 'next/document';
import Script from 'next/script';

export default function Document() {
  return (
    <Html lang="en">
      <Head>
        {/* Google Tag Manager */}
        <Script
          strategy="beforeInteractive"
          id="google-analytics-gtm"
        >
          {`
            (function (w, d, s, l, i) {
              w[l] = w[l] || []; w[l].push({
                'gtm.start': new Date().getTime(), event: 'gtm.js'
              });
              var f = d.getElementsByTagName(s)[0],
                  j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
              j.async = true;
              j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
              f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-MK8NCBSF');
          `}
        </Script>

        {/* Google Analytics */}
        <Script
          strategy="beforeInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-CYZC7Z8D2E"
        />
        <Script
          strategy="beforeInteractive"
          id="google-analytics-gtag"
        >
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag() { dataLayer.push(arguments); }
            gtag('js', new Date());
            gtag('config', 'G-CYZC7Z8D2E');
          `}
        </Script>

        {/* Google Translate Config (you must create this file) */}
        <Script src="/assets/lang-config.js" strategy="beforeInteractive" />

        {/* TranslateInit function (must exist before translate loads) */}
        <Script src="/assets/translation.js" strategy="beforeInteractive" />

        {/* Load Google Translate script (must come after TranslateInit is defined) */}
        <Script src="//translate.google.com/translate_a/element.js?cb=TranslateInit" strategy="afterInteractive" />
      </Head>

      <body>

        <Main />
        <NextScript />
        <div id="google_translate_element" style={{display: "none"}}/>

      </body>
    </Html>
  );
}