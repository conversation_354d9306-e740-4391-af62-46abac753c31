import PropTypes from "prop-types";
import classes from "./styles.module.scss";
import { useId } from "react";

const Select = ({
  value,
  onChange,
  placeholder,
  label,
  second_class = "",
  star = false,
  options_hint,
  hint,
  name,
  required = false,
  disabled = false,
  readOnly = false,
  options = [],
  variant = "default",
}) => {
  const id = useId();

  return (
    <div className={`${classes.select} ${classes[variant] || ""} ${second_class}`}>
      {label && (
        <label htmlFor={id}>
          {label}
          {options_hint && (
            <span className={classes.options_hint}>{options_hint}</span>
          )}
          {(required || star) && (
            <span className={classes.required_star}>*</span>
          )}
        </label>
      )}

      <select
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        required={required || star}
        disabled={disabled || readOnly}
        className={readOnly ? classes.read_only_input : ""}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label || option.desc}
          </option>
        ))}
      </select>

      {hint && <p className={classes.hint}>{hint}</p>}
    </div>
  );
};

Select.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  label: PropTypes.string.isRequired,
  second_class: PropTypes.string,
  star: PropTypes.bool,
  options_hint: PropTypes.string,
  hint: PropTypes.string,
  name: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      desc: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
      disabled: PropTypes.bool,
    })
  ).isRequired,
  variant: PropTypes.string,
};

export default Select;
