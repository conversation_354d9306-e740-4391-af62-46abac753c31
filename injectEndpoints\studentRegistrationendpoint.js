import { apiSlice } from "@/apiSlice/apiSlice";
import endpoints from "@/constants/endpoints";

export const studentApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    addStudentRegistrationDetails: builder.mutation({
      query: (formData) => ({
        url: endpoints.addStudentRegistrationDetails,
        method: "POST",
        body: formData,
      }),
    }),

    getDistrictDropdown: builder.query({
      query: () => ({
        url: endpoints.getDropdown,
        method: "POST",
        body: {
          dropdownType: "district_dropdown",
          replacements: [],
        },
      }),
      transformResponse: (response) =>
        response?.data?.map((d) => d.district) || [],
    }),

    getSubdivisionDropdown: builder.query({
      query: (districtName) => ({
        url: endpoints.getDropdown,
        method: "POST",
        body: {
          dropdownType: "subdivision_dropdown",
          replacements: [districtName],
        },
      }),
      transformResponse: (response) =>
        response?.data?.map((d) => d.subdivision) || [],
    }),

    getStudentRegistrationList: builder.query({
      query: ({ instituteId, registrationId = "%%", pageNo = 1, itemsPerPage = 10, search = "" }) => ({
        url: endpoints.listStudentRegistrationDetails,
        method: "POST",
        body: {
          programCode: "LISTREGISTRATION",
          instituteId: instituteId || "%%",
          registrationId,
          search,
          pageNo,
          itemsPerPage,
        },
      }),
      transformResponse: (response) => response?.data || [],
    }),

    uploadFile: builder.mutation({
      async queryFn({ uploadType, file }) {
        try {
          if (!(file instanceof Blob)) throw new Error("Invalid file type.");

          const formData = new FormData();
          formData.append("programCode", "ADDINSTITUTEPICTURE");
          formData.append("uploadPicture", file);
          formData.append("uploadType", uploadType);

          const token = localStorage.getItem("token");

          const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/upload`, {
            method: "POST",
            headers: {
              Token: token,
            },
            body: formData,
          });

          if (!res.ok) {
            const text = await res.text();
            throw new Error(`Upload failed: ${res.status} ${text}`);
          }

          const data = await res.json();
          return { data };
        } catch (error) {
          return { error };
        }
      },
    }),
    getPaymentDetailsDropdown: builder.query({
      query: (registrationNo) => ({
        url: endpoints.getDropdown,
        method: "POST",
        body: {
          dropdownType: "payment_details",
          replacements: [registrationNo],
        },
      }),
      transformResponse: (response) => response?.data?.[0] || null,
    }),


  }),
});

export const {
  useAddStudentRegistrationDetailsMutation,
  useGetDistrictDropdownQuery,
  useGetSubdivisionDropdownQuery,
  useGetStudentRegistrationListQuery,
  useUploadFileMutation,
  useGetPaymentDetailsDropdownQuery,
} = studentApi;
