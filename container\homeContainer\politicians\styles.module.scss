@import "../../../assets/css/global.scss";

.politicians{
    width: 100%;
    height: auto;
    border-top: 8px solid #571F0B;
    border-bottom: 8px solid #571F0B;
    padding: 100px 0px;

    .politicians_content{
        width: 100%;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .politicians_card{
            display: flex;
            flex-direction: column;
            justify-content: center;

            .politicians_image{
                width: 17vw;
                height: 17vw;
                object-fit: cover;
                border-radius: 50%;
                border: 6px solid #571F0B;
            }

            .name_position{
                border: 4px solid #571F0B33;
                padding: 16px 10px;
                border-radius: 12px;
                margin-top: 2vw;

                .name{
                    text-align: center;
                    color: #833C23;
                    font-size: 20px;
                    font-size: 0.9vw;
                    font-family: $poppinsSemiBold600;
                }

                .position{
                    text-align: center;
                    color: #833C23;
                    font-size: 0.7vw;
                    font-family: $poppinsSemiBold600;
                }
            }
        }

        .middle_content{
            width: 48vw;
            height: auto;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2.6vw 3vw;

            .politicians_card{
                .politicians_image{
                    width: 10vw;
                    height: 10vw;
                }
            }
        }
    }
}