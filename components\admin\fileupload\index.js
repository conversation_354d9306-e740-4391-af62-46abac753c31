"use client";
import React, { useState } from "react";
import PropTypes from "prop-types";
import Input from "../../input";
import styles from "./styles.module.scss";
import { useUploadFileMutation } from "@/injectEndpoints/studentRegistrationendpoint";

const FileUploadModal = ({
  advertisementText,
  onClose,
  onSubmit,
  isVisible,
  inputConfigs = [], // New prop for dynamic input configurations
  ...extraProps
}) => {
  const [file, setFile] = useState(null);
  const [formData, setFormData] = useState({});
  const [fileUrl, setFileUrl] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const [uploadFile] = useUploadFileMutation();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;

    setFile(selectedFile);
    setIsUploading(true);

    try {
      const uploadResponse = await uploadFile({
        uploadType: "ADVERTISEMENT/POLICY",
        file: selectedFile,
      }).unwrap();

      const uploadedUrl = uploadResponse?.data?.data?.url;
      if (!uploadedUrl) throw new Error("Upload failed, no URL returned.");

      setFileUrl(uploadedUrl);
      console.log("File uploaded successfully:", uploadedUrl);
    } catch (error) {
      console.error("Error during file upload:", error);
    } finally {
      setIsUploading(false);
    }
  };
  const handleCancel = () => {
    setFile(null);
    setFileUrl(null);
    setFormData({});
    onClose();
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (onSubmit) {
      onSubmit({
        ...formData,
        notificationDate: new Date().toISOString().split("T")[0],
        notificationFileUrl: fileUrl,
      });
    }

    // Reset form
    setFile(null);
    setFileUrl(null);
    setFormData({});
  };

  if (!isVisible) return null;

  return (
    <div
      className={styles.modalOverlay}
      onClick={onClose}
      role="presentation"
      tabIndex={-1}
      onKeyDown={(e) => {
        if (e.key === "Escape") onClose();
      }}
    >
      <form className={styles.form} onSubmit={handleSubmit} {...extraProps}>
        <span className={styles.formTitle}>Upload your file</span>
        <p className={styles.formParagraph}>{advertisementText}</p>

        <label htmlFor="file-input" className={styles.dropContainer}>
          <input
            type="file"
            required
            id="file-input"
            className={styles.fileInput}
            onChange={handleFileChange}
          />
        </label>

        {isUploading && <p>Uploading...</p>}
        {file && !isUploading && (
          <p style={{ marginTop: "10px", fontSize: "0.9rem" }}>
            Selected file: {file.name}
          </p>
        )}
        {fileUrl && <p>File is ready for submission!</p>}

        {inputConfigs.map((config, index) => (
          <Input
            key={index}
            type={config.type}
            name={config.name}
            placeholder={config.placeholder}
            label={config.label}
            value={formData[config.name] || ""}
            onChange={handleInputChange}
            style={config.style}
            onInput={config.onInput}
            required={config.required}
          />
        ))}
        <div>
          <label>Date:</label>{" "}
          <span>{new Date().toISOString().split("T")[0]}</span>
        </div>

        <div className={styles.buttonGroup}>
          <button
            type="button"
            className={styles.cancelBtn}
            onClick={handleCancel}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.submitBtn}
            disabled={!fileUrl}
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  );
};

FileUploadModal.propTypes = {
  advertisementText: PropTypes.string,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  isVisible: PropTypes.bool.isRequired,
  inputConfigs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      placeholder: PropTypes.string,
      required: PropTypes.bool,
      style: PropTypes.object,
      onInput: PropTypes.func,
    })
  ),
};

FileUploadModal.defaultProps = {
  advertisementText: "You can upload any file type.",
};

export default FileUploadModal;
