"use client";
import React from "react";
import { useRouter } from "next/router";
import styles from "./styles.module.scss";

const Instruction = () => {
  const router = useRouter();

  const handleGoBack = () => {
    router.push("/instituteDashboard/registration");
  };

  const instructions = [
  "मध्यमा परीक्षा में सम्मिलित होने वाले परीक्षार्थी के द्वारा भरे गये निम्नांकित विवरण के आधार पर ही पंजीयन प्रपत्र एवं प्रवेश पत्र निर्गत किये जायेंगे। किसी प्रकार की गलती / त्रुटि रहने की स्थिति में सारी जवाबदेही विद्यालय के परीक्षार्थी / अभिभावक एवं विद्यालय के प्रधान की होगी।",
  "परीक्षार्थी का नाम, पिता का नाम, माता का नाम के पहले (Mr/Mrs/Sri/Smt/Late/Dr/Moulvi/Hafiz) आदि नहीं लिखें।",
  "सभी प्रविष्टियाँ अंग्रेजी के बड़े (Capital) अक्षरों में ही भरें।",
  "सभी प्रविष्टियाँ केवल विद्यालय अभिलेख के आधार पर ही की जाय।",
  "किसी भी गलत प्रविष्टि के लिए विद्यालय प्रधान एवं परीक्षार्थी जिम्मेवार होंगे।",
  "परीक्षार्थी के पिता का नाम ही मान्य होगा। किसी भी स्थिति में विवाहित छात्रा अपने पति का नाम नहीं लिखें।",
  "पंजीयन पत्र, प्रवेश पत्र, अंक पत्र, प्रमाणपत्र, प्रब्रजन प्रमाण पत्र में किसी प्रकार की त्रुटि / भूल का सुधार परीक्षाफल प्रकाशन की तिथि अथवा विद्यालयों को प्रमाण पत्र हस्तगत कराने की तिथि के छह माह के अंदर ही किया जा सकेगा।",
  "त्रुटि / भूल, यदि परीक्षार्थी / विद्यालय स्तर से की गई प्रविष्टि के कारण हुआ है तो सुधार के लिए निर्धारित शुल्क लिया जायेगा।",
  "अतिरिक्त विषय में परीक्षार्थी को परीक्षा में उपस्थित होना अनिवार्य होगा। कुल / प्रतिशत में इसका अतिरिक्त अंक नहीं जोड़ा जायेगा।",
  "परीक्षा में उत्तीर्ण होने के लिये संस्कृत के तीनों पत्रों में अलग-अलग उत्तीर्ण होना अनिवार्य है अर्थात् संस्कृत के प्रत्येक पत्र में न्यूनतम 30 प्रतिशत अंक लाना अनिवार्य होगा।",
  "प्रत्येक विषय में उत्तीर्णांक 30 प्रतिशत निर्धारित है।",
  "परीक्षार्थी के आधार कार्ड अथवा द्वारा प्रदत्त मान्य पहचान पत्र की सत्यापित प्रति संलग्न करना आवश्यक है।"
];


  return (
    <div className={styles.pageWrapper}>
      
      <div className={styles.container}>
        <button className={styles.goBackButton} onClick={handleGoBack}>
          Go Back to Registration
        </button>
        <h2 className={styles.heading}>
          GENERAL INSTRUCTION / <span>सामान्य निर्देश</span>
        </h2>



        <ol className={styles.instructionsList}>
          {instructions.map((instruction, idx) => {
            const key = `instruction-${instruction.slice(0, 20)}-${idx}`;
            return (
              <li key={key}>
                <strong>{idx + 1}:</strong> {instruction}
              </li>
            );
          })}
        </ol>

        {/* <div className={styles.nameBox}>
          {name.map((char, index) => (
            <div key={char + index} className={styles.letterBox}>
              {char === " " ? <>&nbsp;</> : char}
            </div>
          ))}
        </div> */}

        {/* Go Back Button */}
        
      </div>
    </div>
  );
};

export default Instruction;
