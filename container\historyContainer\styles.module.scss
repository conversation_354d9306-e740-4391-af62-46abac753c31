@import "../../assets/css/global.scss";

.history_container{
    width: 100%;
    height: auto;
    padding: 50px 120px;

    .history_content{
        width: 100%;
        height: auto;

        .history_header{
           display: flex;
           justify-content: center;
           margin-bottom: 50px;

            p{
                font-family: $rakkasRegular;
                font-size: 28px;
                color: #571F0B;
                letter-spacing: 3px;
                position: relative;
                padding-bottom: 4px;
                border-bottom: 2px solid #571F0B;
            }
        }

        .histories{
            width: 100%;
            height: auto;

            .paragraphs{
                font-size: 16px;
                font-family: $poppinsRegular400;
                color: #571F0B;
                margin-bottom: 20px;

                span{
                    font-family: $poppinsSemiBold600;
                }
            }

            .center{
                text-align: center;
            }
        }

        .bottom_buttons{
            width: 100%;
            height: auto;
            display: flex;
            justify-content: center;
            gap: 20px;
            
            .tab_btn{
                background-color: transparent;
                border: none;
                outline: none;
                font-size: 18px;
                font-family: $poppinsMedium500;
                color: #571F0B;
                padding: 4px 20px;
                border-radius: 6px;
                cursor: pointer;

                &.active{
                    background-color: #571F0B;
                    color: white;
                }
            }
        }
    }
}
