"use client";
import React, { useState } from "react";
import Pagination from "@/components/admin/pagination";
import styles from "./styles.module.scss";
import FileUploadModal from "@/components/admin/fileupload";
import { FiUpload } from "react-icons/fi";
import { toast } from "react-toastify";
import {
  useGetNotificationsQuery,
  useAddNotificationMutation,
  useDeleteNotificationMutation,
} from "@/injectEndpoints/adminendpoint";

export default function HomePageContainer() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Input configurations for notifications
  const notificationInputConfigs = [
    {
      label: "Advertisement Number:",
      type: "text",
      name: "advertisementNumber",
      placeholder: "Enter Advertisement Number",
      required: true,
      style: { textTransform: "uppercase" },
      onInput: (e) => (e.target.value = e.target.value.toUpperCase()),
    },
    {
      label: "Notification Text:",
      type: "text",
      name: "notificationText",
      placeholder: "Enter Notification Text",
      required: true,
    },
  ];

  const [addNotification] = useAddNotificationMutation();
  const [deleteNotification] = useDeleteNotificationMutation();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const openFileupload = () => setIsModalVisible(true);
  const handleCloseModal = () => setIsModalVisible(false);

  const {
    data: notificationsData,
    error,
    isLoading: isFetchingNotifications,
    refetch: refetchNotifications,
  } = useGetNotificationsQuery({
    programCode: "LISTNOTIFICATION",
    notificationId: "%%",
  });

  const notifications = notificationsData?.data?.data || [];
  const totalItems = notificationsData?.data?.totalCount || 0;

  const handleAddNotification = async (formData) => {
    try {
      await addNotification({
        programCode: "ADDNOTIFICATION",
        notificationText: formData.notificationText,
        notificationDate: formData.notificationDate,
        notificationNumber: formData.advertisementNumber,
        notificationFileUrl: formData.notificationFileUrl,
      }).unwrap();

      toast.success("Notification added successfully!", {
        position: "top-center",
      });
    } catch (err) {
      toast.error("Failed to add notification: " + err.message, {
        position: "top-center",
      });
    } finally {
      handleCloseModal();
    }
  };

  const handleRemove = async (id) => {
    try {
      await deleteNotification({
        programCode: "DELETENOTIFICATION",
        notificationId: id,
      }).unwrap();

      toast.success("Notification deleted successfully!", {
        position: "top-center",
      });
      const result = await refetchNotifications();
      const updatedNotifications = result?.data?.data?.data || [];
      const updatedTotalItems = updatedNotifications.length;
      const maxPage = Math.ceil(updatedTotalItems / itemsPerPage);
      // If current page is now out of bounds, reset to page 1
      if (currentPage > maxPage) {
        setCurrentPage(1);
      }
    } catch (err) {
      toast.error("Failed to delete notification: " + err.message, {
        position: "top-center",
      });
    }
  };

  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentNotifications = notifications.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  if (isFetchingNotifications) {
    return <div>Loading notifications...</div>;
  }

  if (error) {
    return <div>Error loading notifications: {error.message}</div>;
  }

  return (
    <div className={styles.notifications}>
      <div className={styles.header}>
        <h3 className={styles.title}>View/Upload Notifications:</h3>
        <button className={styles.addBtn} onClick={openFileupload}>
          <FiUpload /> &nbsp; Upload New
        </button>
      </div>

      <div className={styles.list}>
        {currentNotifications.length > 0 ? (
          currentNotifications.map((note) => (
            <div
              key={note.notificationId}
              className={styles.item}
              id={`notification-${note.notificationId}`}
              data-id={note.notificationId}
            >
              <p>{note.notificationText}</p>
              <div className={styles.actions}>
                <a
                  href={note.notificationFileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.edit}
                >
                  View
                </a>

                <button
                  className={styles.remove}
                  onClick={() => handleRemove(note.notificationId)}
                >
                  Remove
                </button>
              </div>
            </div>
          ))
        ) : (
          <p className={styles.empty}>No notifications found.</p>
        )}
      </div>

      <FileUploadModal
        advertisementText="You can upload any file type."
        onClose={handleCloseModal}
        onSubmit={handleAddNotification}
        isVisible={isModalVisible}
        inputConfigs={notificationInputConfigs}
      />

      <Pagination
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
      />
    </div>
  );
}
