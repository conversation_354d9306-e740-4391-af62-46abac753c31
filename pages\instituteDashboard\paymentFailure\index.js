"use client";
import React from "react";
import FailureModal from "@/container/instituteDashboardContainer/failurePayment";
import { useSearchParams } from "next/navigation";

const PaymentFailurePage = () => {
  const searchParams = useSearchParams();
  const txnid = searchParams.get("txnid");

  return (
    <div>
      <FailureModal txnid={txnid} />
    </div>
  );
};

export default PaymentFailurePage;
