@import "../../assets/css/global.scss";

.select{
    label{
        display: block;
        margin-bottom: 10px;
        font-family: $poppinsMedium500;
    }

    select{
        border: 1px solid $black;
        outline: unset;
        padding: 5px 10px;
        border-radius: 6px;
        font-family: $poppinsMedium500;
    }
}

.v_one {
    position: relative;
    display: inline-block;

    select {
        background-color: $reddishBrown;
        border: 1px solid $reddishBrown;
        color: white;
        padding: 4px 16px;
        padding-right: 30px;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: none;
        z-index: 2;
        position: relative;
    }

    &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 5px;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/icons/SortDown.svg');
        background-repeat: no-repeat;
        background-size: contain;
        pointer-events: none;
        z-index: 3;
    }
}