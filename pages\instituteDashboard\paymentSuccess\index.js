"use client";
import React from "react";
import { useSearchParams } from "next/navigation";
import SuccessModal from "@/container/instituteDashboardContainer/scucessPayment";

const PaymentSuccessPage = () => {
  const searchParams = useSearchParams();
  const txnid = searchParams.get("txnid");

  return (
    <div>
      <SuccessModal txnid={txnid} />
    </div>
  );
};

export default PaymentSuccessPage;
