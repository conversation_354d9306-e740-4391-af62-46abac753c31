"use client";
import React, { useEffect } from "react";
import { useFormikContext } from "formik";
import Label from "@/components/formSection/label";
import SelectForm from "@/components/formSection/selectForm";
import styles from "../styles.module.scss";

const Handicap = () => {
  const { values, setFieldValue } = useFormikContext();
  const isHandicapped = values?.handicapped;

  useEffect(() => {
    if (isHandicapped !== "Yes") {
      setFieldValue("handicappedDetails", []);
    }
  }, [isHandicapped, setFieldValue]);

  return (
    <div>
      <div className={styles.sectioncategory}>
        <SelectForm type="radio" name="handicapped" options={["Yes", "No"]} />
      </div>

      {isHandicapped === "Yes" && (
        <div className={styles.twoColumnLayout}>
          <Label label="SPECIAL ABILITY DETAILS :" htmlFor="handicappedDetails" required />
          <div className={styles.options}>
            <SelectForm
              type="checkbox"
              name="handicappedDetails"
              options={["Blind", "Deaf", "Physically Handicapped", "Dyslexic", "Spastic"]}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Handicap;
