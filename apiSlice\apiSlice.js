import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_URL } from "@/config";

const baseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  prepareHeaders: (headers, { getState }) => {
    const token = getState()?.auth?.token || localStorage.getItem("token");
    const language = getState()?.auth?.language;

    if (token) headers.set("Token", token);
    if (language) headers.set("Accept-Language", language);

    if (!headers.has("Content-Type")) headers.set("Content-Type", "application/json");

    return headers;
  },
});

const baseQueryWithInterceptor = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);

  if (
    result?.error?.status === 400 &&
    result?.error?.data?.message?.toLowerCase() === "jwt expired"
  ) {
    const alreadyExpired = localStorage.getItem("sessionExpired");
    if (!alreadyExpired) {
      localStorage.setItem("sessionExpired", "true");
      window.location.reload();
    }
  }

  return result;
};

export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithInterceptor,
  tagTypes: ["Student"],
  endpoints: () => ({}),
});
