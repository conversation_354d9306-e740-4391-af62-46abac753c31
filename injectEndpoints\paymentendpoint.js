// paymentApi.js
import { apiSlice } from "@/apiSlice/apiSlice";
import endpoints from "@/constants/endpoints";

export const paymentApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    /* --------------------------
       Initiate Payment (via GET with query params)
    --------------------------- */
    initiatePayment: builder.mutation({
      query: ({ registrationId, productinfo }) => {
        const params = new URLSearchParams({
          registrationId,
          productinfo,
        }).toString();

        return {
          url: `${endpoints.initiatePayment}?${params}`, 
          method: "GET",
        };
      },
    }),
  getPaymentStatus: builder.mutation({
      query: ({ programCode, txnid }) => ({
        url: `${endpoints.paymentStatus}`, 
        method: "POST",
        body: { programCode, txnid },
      }),
    }),
  }),
  overrideExisting: false,
});

// ✅ Hooks
export const { 
  useInitiatePaymentMutation,
  useGetPaymentStatusMutation 
} = paymentApi;