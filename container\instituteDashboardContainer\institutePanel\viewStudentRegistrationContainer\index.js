"use client";
import React, { useEffect, useState } from "react";
import styles from "./styles.module.scss";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { useGetStudentRegistrationListQuery } from "@/injectEndpoints/studentRegistrationendpoint";
import RegistrationPreview from "@/components/view-registration-details";
import { API_URL } from "@/config";

const ITEMS_PER_PAGE = 10;

const ViewRegistrationPage = () => {
  const router = useRouter();
  const instituteId = useSelector((state) => state.auth?.instituteDetails?.id);
  const [successPage, setSuccessPage] = useState(1);
  const [failurePage, setFailurePage] = useState(1);
  const [aadharSearch, setAadharSearch] = useState("");
  const [formNoSearch, setFormNoSearch] = useState("");
  const [selectedRegistration, setSelectedRegistration] = useState(null);
  const [viewMode, setViewMode] = useState("success");
  const [mounted, setMounted] = useState(false);

  useEffect(() => setMounted(true), []);

  const { data, isLoading, isError } = useGetStudentRegistrationListQuery(
    {
      instituteId,
      registrationId: "%%",
      pageNo: 1,
      itemsPerPage: 1000,
      search: "",
    },
    { skip: !instituteId }
  );

  const registrations = data?.data || [];

  const normalize = (val) =>
    val?.toString().replace(/[\s-]/g, "").toLowerCase() || "";

  const successfulRegistrations = registrations.filter(
    (reg) => reg.paymentStatus?.toLowerCase() === "success"
  );
  const failedRegistrations = registrations.filter(
    (reg) => reg.paymentStatus?.toLowerCase() !== "success"
  );

  const applyFilters = (list) =>
    list.filter((reg) => {
      const matchesAadhar = aadharSearch
        ? normalize(reg.aadharNo || reg.documentNumber).includes(
          normalize(aadharSearch)
        )
        : true;
      const matchesFormNo = formNoSearch
        ? normalize(reg.registrationNo).includes(normalize(formNoSearch))
        : true;
      return matchesAadhar && matchesFormNo;
    });

  const filteredRegistrations =
    viewMode === "success"
      ? applyFilters(successfulRegistrations)
      : applyFilters(failedRegistrations);

  const currentPage = viewMode === "success" ? successPage : failurePage;
  const totalCount = filteredRegistrations.length;
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedRegistrations = filteredRegistrations.slice(startIndex, endIndex);

  const getPageNumbers = () => {
    const pages = [];
    const maxVisible = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);
    if (end - start < maxVisible - 1) start = Math.max(1, end - maxVisible + 1);
    for (let i = start; i <= end; i++) pages.push(i);
    return pages;
  };

  const handleRePayment = (registration) => {
    if (!registration?.id && !registration?.registrationId) {
      alert("Invalid registration record. Cannot process repayment.");
      return;
    }

    if (
      !confirm(
        `Are you sure you want to retry payment for ${registration.studentNameEnglish}?`
      )
    )
      return;

    try {
      const registrationId = registration?.id || registration?.registrationId;
      const payUrl = `${API_URL}/payment/payment-initiate?registrationId=${registrationId}&productinfo=form`;
      window.location.href = payUrl;
    } catch (error) {
      console.error("Error initiating repayment:", error);
      alert("Unable to start repayment. Please try again.");
    }
  };

  if (!mounted) return null;

  return (
    <div className={styles.container}>
      {!selectedRegistration ? (
        <>
          <div className={styles.buttonRow}>
            <div className={styles.halfButton}>
              <button type="button" onClick={() => router.push("/instituteDashboard")}>
                ← Go Back to Dashboard
              </button>
            </div>
          </div>

          <h2 className={styles.pageTitle}>Student Registrations</h2>

          <div className={styles.buttonRow}>
            <div className={styles.halfButton}>
              <button
                type="button"
                className={viewMode === "success" ? styles.activeToggle : ""}
                onClick={() => setViewMode("success")}
              >
                ✅ Successful Registrations
              </button>
            </div>
            <div className={styles.halfButton}>
              <button
                type="button"
                className={viewMode === "failure" ? styles.activeToggle : ""}
                onClick={() => setViewMode("failure")}
              >
                ❌ Unsuccessful Registrations
              </button>
            </div>
          </div>

          <div className={styles.searchBar}>
            <input
              type="text"
              placeholder="Search by Aadhaar"
              value={aadharSearch}
              onChange={(e) => setAadharSearch(e.target.value)}
              className={styles.searchInput}
            />
            <input
              type="text"
              placeholder="Search by Registration No"
              value={formNoSearch}
              onChange={(e) => setFormNoSearch(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <table className={styles.table}>
            <thead>
              <tr>
                <th>#</th>
                <th>Registration No</th>
                <th>Student Name</th>
                <th>Aadhaar No / ID</th>
                <th>DOB</th>
                <th>Category</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {isLoading && (
                <tr>
                  <td colSpan={8} className={styles.loading}>
                    ⏳ Loading registrations...
                  </td>
                </tr>
              )}

              {isError && (
                <tr>
                  <td colSpan={8} className={styles.error}>
                    ❌ Failed to load registrations. Please try again.
                  </td>
                </tr>
              )}

              {!isLoading && paginatedRegistrations.length === 0 && (
                <tr>
                  <td colSpan={8} className={styles.noData}>
                    📭 No registrations found
                  </td>
                </tr>
              )}

              {!isLoading &&
                paginatedRegistrations.map((reg, index) => (
                  <tr key={reg.registrationNo || index}>
                    <td>{(currentPage - 1) * ITEMS_PER_PAGE + index + 1}</td>
                    <td>{reg.registrationNo || "—"}</td>
                    <td>{reg.studentNameEnglish || "—"}</td>
                    <td>{reg.aadharNo || reg.documentNumber || "N/A"}</td>
                    <td>
                      {reg.studentDob
                        ? new Date(reg.studentDob).toLocaleDateString("en-IN", {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        })
                        : "—"}
                    </td>
                    <td>{reg.studentCategory || "—"}</td>
                    <td>
                      {reg.paymentStatus?.toLowerCase() === "success" ? (
                        <span className={styles.successStatus}>✅ Success</span>
                      ) : (
                        <span className={styles.failedStatus}>❌ Not Paid</span>
                      )}
                    </td>
                    <td className={styles.actions}>
                      {reg.paymentStatus?.toLowerCase() === "success" ? (
                        <button onClick={() => setSelectedRegistration(reg)}>
                          👁 View
                        </button>
                      ) : (
                        <>
                          <button onClick={() => setSelectedRegistration(reg)}>
                            👁 View
                          </button>
                          <button
                            onClick={() => handleRePayment(reg)}
                            className={styles.repayButton}
                          >
                            💳 Repay
                          </button>
                        </>
                      )}
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>

          {totalPages > 1 && (
            <div className={styles.pagination}>
              <button
                disabled={currentPage === 1}
                onClick={() =>
                  viewMode === "success"
                    ? setSuccessPage(currentPage - 1)
                    : setFailurePage(currentPage - 1)
                }
                className={styles.pageBtn}
              >
                ⬅ Prev
              </button>

              {getPageNumbers().map((num) => (
                <button
                  key={num}
                  onClick={() =>
                    viewMode === "success"
                      ? setSuccessPage(num)
                      : setFailurePage(num)
                  }
                  className={`${styles.pageBtn} ${currentPage === num ? styles.activePage : ""
                    }`}
                >
                  {num}
                </button>
              ))}

              <button
                disabled={currentPage === totalPages}
                onClick={() =>
                  viewMode === "success"
                    ? setSuccessPage(currentPage + 1)
                    : setFailurePage(currentPage + 1)
                }
                className={styles.pageBtn}
              >
                Next ➡
              </button>
            </div>
          )}
        </>
      ) : (
        <>
          <div className={styles.buttonRow}>
            <div className={styles.halfButton}>
              <button type="button" onClick={() => setSelectedRegistration(null)}>
                ← Back to List
              </button>
            </div>
          </div>

          <div id="registration-preview">
            <RegistrationPreview registration={selectedRegistration} />
          </div>

          <div className={styles.buttonRow}>
            <div className={styles.halfButton}>
              <button
                type="button"
                onClick={() => {
                  const printContents =
                    document.getElementById("registration-preview").innerHTML;
                  const originalContents = document.body.innerHTML;
                  document.body.innerHTML = printContents;
                  window.print();
                  document.body.innerHTML = originalContents;
                  window.location.reload();
                }}
              >
                🖨️ Print
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ViewRegistrationPage;
