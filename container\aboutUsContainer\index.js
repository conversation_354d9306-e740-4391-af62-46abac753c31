import classes from "./styles.module.scss";
import Image from "next/image";
import { useRouter } from "next/router";
import Link from "next/link";

const AboutUsContainer = () => {

    const router = useRouter();

    return(
        <div className={classes.about_us_container}>
            <div className={classes.about_us_content}>
                <div className={classes.section_one}>
                    <div className={classes.section_one_left}>
                        <p className={classes.about_us_header}>About Us</p>
                        <p className={classes.big_header}>
                            History of the Sanskrit<br /> Education in Bihar
                        </p>
                        <p className={classes.desc}>
                            Exploring Bihar&apos;s Timeless Contribution to Sanskrit<br /> 
                            Education — From Ancient Centers of Learning to<br /> 
                            Modern Educational Reforms.
                        </p>

                        <button className={classes.learn_more} onClick={() => router.push("/history-of-sanskrit")}>Learn More</button>
                    </div>
                    <div className={classes.section_one_right}>
                        <div className={classes.right_image}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/nalanda-university.webp`} width={1204} height={674} alt="university" />
                        </div>
                        <div className={classes.bg_one}></div>
                        <div className={classes.bg_two}></div>
                    </div>
                </div>

                <div className={classes.section_two}>
                    <div className={classes.mission_vision_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Purposefulman.svg`} width={100} height={100} alt="mission_vision" className={classes.msn_vsn_img} />
                        <div className={classes.right_text}>
                            <p className={classes.right_text_header}>Our Mission</p>
                            <p className={classes.right_text_desc}>To preserve, promote, and revitalize the timeless tradition of Sanskrit education in Bihar by integrating ancient wisdom with modern pedagogy and institutional support.</p>
                        </div>
                    </div>
                    <div className={classes.mission_vision_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Innovation.svg`} width={100} height={100} alt="mission_vision" className={classes.msn_vsn_img} />
                        <div className={classes.right_text}>
                            <p className={classes.right_text_header}>Our Vision</p>
                            <p className={classes.right_text_desc}>To re-establish Bihar as a global center of Sanskrit learning by nurturing excellence in traditional scholarship and bridging it with contemporary relevance.</p>
                        </div>
                    </div>
                </div>

                <div className={classes.section_three}>
                    <div className={classes.policies_header}>
                        <p>Policies</p>
                    </div>
                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/adhyadesh%201981.pdf"} target="_blank">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार संस्कृत शिक्षा बोर्ड अध्यादेश, 1981</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/BSSB%20Adhiniyam%201981.pdf"} target="_blank">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार संस्कृत शिक्षा बोर्ड अधिनियम, 1981</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/BSSB%20prabandhan%20niymabali%201978.pdf"} target="_blank">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार राज्य गैर-सरकारी संस्कृत उच्च विद्यालय (सेवाशर्त) नियमावली, 1976</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/BSSB%20niymabali%201978.pdf"}>
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार राज्य अराजकीय संस्कृत विद्यालय प्रबन्ध समिति नियमावली, 1978</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/1993%20Prasukrit%20Niymabali%20%20%20Final%20Fresh.PDF"} target="_black">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार शिक्षा बोर्ड अधिनयम 1981 के अन्तर्गत नियमावली, 1993 एवं 2013</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/prabandhan%20samiti%20gathan%202015%C2%A0ka%C2%A0hai.pdf"} target="_blank">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार राज्य अराजकीय प्रस्वीकृत संस्कृत विद्यालय (मध्यमा स्तर तक) प्रबंध समिति गठन नियमावली, 2015</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/policies/eGazette-GazettePublished-333_2_2015  Letter No.- 99.pdf"} target="_blank">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार राज्य अराजकीय प्रस्वीकृत संस्कृत विद्यालय (मध्यमा स्तर तक) के शिक्षकों की सेवा शर्त नियमावली, 2015</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>

                    {/* <Link className={classes.policies_card} href={""}>
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>बिहार संस्कृत शिक्षा बोर्ड परीक्षा नियमावली, 1994</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link> */}

                    <Link className={classes.policies_card} href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/1.pdf"} target="_blank">
                        <div className={classes.left_part}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/GoogleDocs.svg`} width={30} height={30} alt="icon" className={classes.doc_icon} />
                            <p className={classes.policies_card_header}>पाठ्यक्रम एवं पाठ्य ग्रंथावली, वर्ष-1998 - वर्ग -1 से 10 तक</p>
                        </div>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/UpLeft.svg`} width={22} height={22} alt="icon" className={classes.right_arrow} />
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default AboutUsContainer;