"use client";
import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useFormikContext } from "formik";
import Image from "next/image";
import CloseRoundedIcon from "@mui/icons-material/CloseRounded"; 
import { useUploadFileMutation } from "@/injectEndpoints/studentRegistrationendpoint";
import styles from "../styles.module.scss";

const GeoTaggedPhotos = ({ existingPhotos = [], refresh, deletePhoto }) => {
  const { values, setFieldValue } = useFormikContext();
  const [uploadFile] = useUploadFileMutation();
  const [showSubmit, setShowSubmit] = useState(false);
  const [startIndex, setStartIndex] = useState(0);

  const maxPhotos = 5;
  const displayCount = 2;
  const [loadingMap, setLoadingMap] = useState({});

  useEffect(() => {
    if (existingPhotos.length) {
      const photoUrls = existingPhotos.map((item) => ({
        id: item.id,
        url: item.institutePicture,
      }));
      setFieldValue("uploads.existingGeoPhotos", photoUrls);
    }
  }, [existingPhotos, setFieldValue]);

  const addNewPhoto = () => {
    if ((values.uploads.geoPhotos?.length || 0) >= maxPhotos) return;
    setFieldValue("uploads.geoPhotos", [
      ...(values.uploads.geoPhotos || []),
      { id: Date.now(), url: "", isNew: true },
    ]);
  };

  const handleFileChange = async (e, index) => {
    const file = e.target.files[0];
    if (!file) return;
    if (!["image/png", "image/jpeg"].includes(file.type)) return;

    setLoadingMap((prev) => ({ ...prev, [index]: true }));

    try {
      const res = await uploadFile({ file, uploadType: "geoPhoto" }).unwrap();
      const fileUrl = res?.data?.data?.url;
      if (!fileUrl) throw new Error("Upload failed");

      const updatedPhotos = [...(values.uploads.geoPhotos || [])];
      updatedPhotos[index] = { id: Date.now(), url: fileUrl, isNew: true, file };
      setFieldValue("uploads.geoPhotos", updatedPhotos);
    } catch (err) {
      console.error(err);
    } finally {
      setLoadingMap((prev) => ({ ...prev, [index]: false }));
      setShowSubmit(true);
    }
  };

  const removeNewPhoto = (index) => {
    const updatedPhotos = [...(values.uploads.geoPhotos || [])];
    updatedPhotos.splice(index, 1);
    setFieldValue("uploads.geoPhotos", updatedPhotos);
    if (!updatedPhotos.length) setShowSubmit(false);

    setLoadingMap((prev) => {
      const copy = { ...prev };
      delete copy[index];
      return copy;
    });
  };

  const handleDeleteExisting = async (id) => {
    if (!deletePhoto) return;
    try {
      await deletePhoto({ pictureId: id }).unwrap();
      const updatedExisting = values.uploads.existingGeoPhotos.filter((p) => p.id !== id);
      setFieldValue("uploads.existingGeoPhotos", updatedExisting);
      if (typeof refresh === "function") refresh();
      if (updatedExisting.length <= startIndex)
        setStartIndex(Math.max(0, updatedExisting.length - displayCount));
    } catch (err) {
      console.error(err);
    }
  };

  const handlePrev = () => setStartIndex(Math.max(startIndex - displayCount, 0));
  const handleNext = () => {
    const total = values.uploads.existingGeoPhotos?.length || 0;
    setStartIndex(Math.min(startIndex + displayCount, Math.max(total - displayCount, 0)));
  };

  const visiblePhotos =
    values.uploads.existingGeoPhotos
      ?.slice(startIndex, startIndex + displayCount)
      .concat(
        Array(
          Math.max(
            0,
            displayCount -
              (values.uploads.existingGeoPhotos?.slice(startIndex, startIndex + displayCount)
                ?.length || 0)
          )
        ).fill(null)
      ) || [];

  const totalPhotos = values.uploads.existingGeoPhotos?.length || 0;

  useEffect(() => {
    if ((values.uploads.geoPhotos?.length || 0) === 0) setShowSubmit(false);
  }, [values.uploads.geoPhotos]);

  return (
    <div className={styles.photoSection}>
      {totalPhotos > 0 && (
        <>
          <div className={styles.photoHeader}>
            <h3>Institute Photos</h3>
          </div>
          <div className={styles.carouselWrapper}>
            {startIndex > 0 && (
              <button type="button" onClick={handlePrev} className={styles.navBtn}>
                &#8249;
              </button>
            )}
            <div className={styles.photos}>
              {visiblePhotos.map((photo, idx) =>
                photo ? (
                  <div key={photo.id} className={styles.photoItem}>
                    <button
                      type="button"
                      className={styles.imageBtn}
                      onClick={() => window.open(photo.url, "_blank")}
                    >
                      <Image
                        src={photo.url}
                        alt="Institute"
                        fill
                        style={{ objectFit: "contain" }}
                        className={styles.schoolImage}
                      />
                    </button>
                    <button
                      type="button"
                      className={styles.removeBtn}
                      onClick={() => handleDeleteExisting(photo.id)}
                      title="Remove Photo"
                    >
                      <CloseRoundedIcon fontSize="small" />
                    </button>
                  </div>
                ) : (
                  <div key={`placeholder-${startIndex + idx}`} className={styles.photoItem}></div>
                )
              )}
            </div>

            {startIndex + displayCount < totalPhotos && (
              <button type="button" onClick={handleNext} className={styles.navBtn}>
                &#8250;
              </button>
            )}
          </div>
        </>
      )}

      <div className={styles.photoHeader} style={{ marginTop: "20px" }}>
        <h3>Upload New Photos</h3>
        <button
          type="button"
          className={styles.addNew}
          onClick={addNewPhoto}
          disabled={(values.uploads.geoPhotos?.length || 0) >= maxPhotos}
        >
          + Add New
        </button>
      </div>
      <div className={styles.uploadList}>
        {values.uploads.geoPhotos?.map((photo, index) => (
          <div key={photo.id} className={styles.uploadRow}>
            {!photo.url ? (
              <input
                type="file"
                accept="image/png,image/jpeg"
                onChange={(e) => handleFileChange(e, index)}
                disabled={loadingMap[index]}
              />
            ) : (
              <>
                <span className={styles.urlText}>
                  {photo.url.length > 40 ? photo.url.slice(0, 40) + "..." : photo.url}
                </span>
                <button
                  type="button"
                  className={styles.removeBtn}
                  onClick={() => removeNewPhoto(index)}
                  disabled={loadingMap[index]}
                  title="Remove Photo"
                >
                  <CloseRoundedIcon fontSize="small" />
                </button>
              </>
            )}
            {loadingMap[index] && <span className={styles.loadingText}>Uploading...</span>}
          </div>
        ))}
      </div>

      {showSubmit && values.uploads.geoPhotos?.length > 0 && (
        <button type="submit" className={styles.filledBtn}>
          Submit Photos
        </button>
      )}
    </div>
  );
};

GeoTaggedPhotos.propTypes = {
  existingPhotos: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number,
      institutePicture: PropTypes.string.isRequired,
    })
  ),
  refresh: PropTypes.func,
  deletePhoto: PropTypes.func,
};

export default GeoTaggedPhotos;
