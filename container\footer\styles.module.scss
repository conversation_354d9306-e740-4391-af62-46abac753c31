@import "../../assets/css/global.scss";

.footer_container{
    width: 100%;
    height: auto;
    background-color: $darkBrown;
    padding: 36px 88px;

    .footer_content{
        width: 100%;
        height: auto;

        .top_footer{
            width: 100%;
            height: auto;
            display: flex;
            justify-content: space-between;

            .logo_address{
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .logos{
                    img{
                        margin: 0px 15px;
                        width: 60px;
                        height: auto;
                    }
                }

                .address_sec{
                    margin-top: 20px;
                    
                    .hindi_heading{
                        text-align: center;
                        font-size: 19.5px;
                        font-family: $rockWellExtraBold;
                        color: $white;
                        font-weight: 800;
                    }

                    .english_heading{
                        text-align: center;
                        font-family: $robotoLight300;
                        color: $white;
                        font-size: 15px;
                        margin-top: 2px;
                    }

                    .address{
                        text-align: center;
                        font-family: $poppinsRegular400;
                        color: $white;
                        font-size: 13px;
                        margin-top: 3px;
                    }
                }
            }

            .footer_nav_links{
                .links_header{
                    color: $white;
                    font-family: $poppinsBold700;
                    font-size: 20px;
                }

                .links{
                    a, .a{
                        color: $white;
                        display: block;
                        text-decoration: unset;  
                        font-family: $poppinsRegular400;  
                        font-size: 15px;
                        margin-top: 10px;
                        cursor: pointer;

                        &:hover{
                            transform: translateX(10px);
                        }
                    }

                    .a{
                        display: flex;
                        align-items: center;

                        img{
                            width: auto;
                            height: 16px;
                            margin-right: 5px;
                        }
                    }

                    .social_icons{
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        margin-top: 20px;

                        a:hover{
                            transform: unset;
                        }

                        .s_icon{
                            width: 40px;
                            height: auto;
                            cursor: pointer;

                            &:hover{
                                scale: 1.1;
                            }
                        }
                    }
                }
            }
        }
        
        .bottom_footer{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40px;
            border-top: 1px solid $borderTwo;
            padding-top: 10px;

            .left_text{
                color: $white;
                font-family: $poppinsRegular400;
                font-size: 14px;
            }

            .privacy_terms_services{
                display: flex;
                gap: 40px;
                
                a{
                    color: $white;
                    font-family: $poppinsRegular400;
                    font-size: 14px;
                    text-decoration: unset;
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px){
    .footer_container{
        padding: 30px 65px;

        .footer_content{
            .top_footer{
                .logo_address{
                    .logos{
                        img{
                            margin: 0px 13px;
                            width: 55px;
                        }
                    }

                    .address_sec{
                        margin-top: 10px;
                        
                        .hindi_heading{
                            font-size: 17.5px;
                        }

                        .english_heading{
                            font-size: 14px;
                            margin-top: 1px;
                        }

                        .address{
                            font-size: 12px;
                            margin-top: 1px;
                        }
                    }
                }

                .footer_nav_links{
                    .links_header{
                        font-size: 18px;
                    }

                    .links{
                        a, .a{
                            font-size: 14px;
                            margin-top:8px;

                            &:hover{
                                transform: translateX(8px);
                            }
                        }

                        .a{
                            img{
                                width: auto;
                                height: 15px;
                            }
                        }
                    }
                }
            }
            
            .bottom_footer{
                margin-top: 35px;

                .left_text{
                    font-size: 13px;
                }

                .privacy_terms_services{
                    gap: 35px;
                    
                    a{
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 980px){
    .footer_container{
        padding: 40px 30px;

        .footer_content{
            .top_footer{
                .logo_address{
                    .logos{
                        img{
                            margin: 0px 10px;
                            width: 45px;
                        }
                    }

                    .address_sec{
                        .hindi_heading{
                            font-size: 16px;
                        }

                        .english_heading{
                            font-size: 13px;
                            margin-top: 0px;
                        }

                        .address{
                            font-size: 11px;
                            margin-top: 0px;
                        }
                    }
                }

                .footer_nav_links{
                    .links_header{
                        font-size: 16px;
                    }

                    .links{
                        a, .a{
                            font-size: 13px;
                            margin-top: 5px;
                        }

                        .a{
                            img{
                                height: 14px;
                            }
                        }
                    }
                }
            }
            
            .bottom_footer{
                margin-top: 30px;
                padding-top: 7px;

                .left_text{
                    font-size: 12px;
                }

                .privacy_terms_services{
                    gap: 30px;
                    
                    a{
                        font-size: 12px;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 667px){
    .footer_container{
        padding: 30px 15px;

        .footer_content{
            .top_footer{
                flex-wrap: wrap;
                gap: 30px;
                justify-content: center;

                .logo_address{
                    width: calc(50% - 15px);
                    .logos{
                        img{
                            margin: 0px 10px;
                            width: 45px;
                        }
                    }

                    .address_sec{
                        .hindi_heading{
                            font-size: 15px;
                        }

                        .english_heading{
                            font-size: 12px;
                            margin-top: 1px;
                        }

                        .address{
                            font-size: 10px;
                            margin-top: 1px;
                        }
                    }
                }

                .footer_nav_links{
                    width: calc(50% - 15px);
                    .links_header{
                        font-size: 16px;
                    }

                    .links{
                        a, .a{
                            font-size: 12.5px;
                            margin-top: 5px;
                        }

                        .a{
                            img{
                                height: 13px;
                            }
                        }
                    }
                }
            }
            
            .bottom_footer{
                margin-top: 30px;
                padding-top: 7px;
                flex-direction: column;
                gap: 5px;

                .left_text{
                    font-size: 12px;
                }

                .privacy_terms_services{
                    gap: 30px;
                    
                    a{
                        font-size: 12px;
                    }
                }
            }
        }
    }
}