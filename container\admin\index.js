"use client";
import React, { useState } from "react";
import { useRouter } from "next/router";
import Sidebar from "@/components/admin/sidebar";
import AdminDashboardHeader from "@/components/admin/adminDashboardheader";
import HomePageContainer from "@/container/admin/home";
import PoliciesContainer from "@/container/admin/policies";
import AcademicsContainer from "@/container/admin/academics";
import InstitutionsContainer from "@/container/admin/institutions";
import AdminGalleryContainer from "@/container/admin/gallery";
import AdminLibraryContainer from "@/container/admin/adminLibrary";
import styles from "./styles.module.scss";

export default function AdminDashboard() {
  const [formNoSearch, setFormNoSearch] = useState("");
  const router = useRouter();

  // Function to render the appropriate container based on current route
  const renderPageContent = () => {
    switch (router.pathname) {
      case "/admin":
        return <HomePageContainer />;
      case "/admin/policies":
        return <PoliciesContainer />;
      case "/admin/academics":
        return <AcademicsContainer />;
      case "/admin/Institutions":
        return <InstitutionsContainer />;
      case "/admin/gallery":
        return <AdminGalleryContainer />;
      case "/admin/adminLibrary":
        return <AdminLibraryContainer />;
      default:
        return <HomePageContainer />;
    }
  };

  return (
    <div className={styles.layout}>
      {/* Sidebar */}
      <Sidebar />

      {/* Main Dashboard */}
      <main className={styles.mainContent}>
        <div className={styles.dashboard}>
          {/* Header */}
          <AdminDashboardHeader
            username="Admin0029"
            searchValue={formNoSearch}
            setSearchValue={setFormNoSearch}
          />

          {/* Page Content - Dynamically rendered based on route */}
          {renderPageContent()}

          {/* Optional: just for demo, show live search value */}
          {formNoSearch && (
            <p className={styles.searchResult}>
              Searching for: <strong>{formNoSearch}</strong>
            </p>
          )}
        </div>
      </main>
    </div>
  );
}
