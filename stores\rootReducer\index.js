import { combineReducers } from "@reduxjs/toolkit";
import authReducer from "@/features/auth/authSlice";
import formReducer from "@/features/form/formSlice";
import { apiSlice } from "@/apiSlice/apiSlice"; 

// Combine reducers
const appReducer = combineReducers({
  auth: authReducer,
  form: formReducer,
  [apiSlice.reducerPath]: apiSlice.reducer, 
});

// Root reducer with logout reset
const rootReducer = (state, action) => {
  if (action.type === "auth/logout") {
    state = undefined;
  }
  return appReducer(state, action);
};

export default rootReducer;
