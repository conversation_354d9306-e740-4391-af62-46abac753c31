$page-bg: #f8f1e6;
$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;

.notifications {
  background: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 17px;
      color: $primary;
      font-weight: bolder;
    }

    .addBtn {
      background: $primary;
      color: #fff;
      border: none;
      padding: 8px 14px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;

      font-weight: 500;
      transition: background 0.2s ease;

      &:hover {
        background: darken($primary, 5%);
      }
    }
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .item {
      border: 1px solid $primary;
      border-radius: 12px;
      padding: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      p {
        margin: 0;
        font-size: 14px;
        color: $primary;
        flex: 1;
        font-weight: 600;
      }

      .actions {
        display: flex;
        gap: 8px;

        .remove {
          background: transparent;
          border: 1px solid $primary;
          color: $primary;
          padding: 7px 14px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 13px;
          transition: all 0.2s ease;

          &:hover {
            background: lighten($primary, 5%);
            color: #fff;
          }
        }

        .edit {
          background: $primary;
          border: none;
          color: #fff;
          padding: 7px 12px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 13px;
          transition: all 0.2s ease;
          text-decoration: none;

          &:hover {
            background: darken($primary, 5%);
          }
        }
      }
    }

    .empty {
      text-align: center;
      color: #888;
      font-size: 14px;
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    gap: 6px;
    margin-top: 10px;

    .pageBtn {
      border: 1px solid $primary;
      background: transparent;
      padding: 6px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 13px;
      color: $primary;
      transition: all 0.2s ease;

      &.active {
        background: $primary;
        color: #fff;
      }

      &:hover {
        background: lighten($primary, 45%);
      }
    }
  }
}
