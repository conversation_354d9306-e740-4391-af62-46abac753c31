@import "../../assets/css/global.scss";

.gallery_card{
    border: 1px solid #571F0B;
    border-radius: 6px;
    width: 230px;
    height: auto;
    aspect-ratio: 230/227;
    overflow: hidden;
    position: relative;

    .icon_filled, .icon_border{
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
    }

    .icon_border{
        color: white;
    }

    .icon_filled{
        color: red;
    }

    .card_img{
        width: 100%;
        height: auto;
        aspect-ratio: 229/227;
        border-radius: 0px 0px 6px 6px;
        object-fit: cover;
    }

    .card_texts{
        padding: 5px 10px;
        .card_title{
            font-size: 14px;
            font-family: $poppinsMedium500;
            color: #571F0B;
        }
        .date{
            font-size: 12px;
            font-family: $poppinsLight300;
            color: #571F0B;
        }
    }
}