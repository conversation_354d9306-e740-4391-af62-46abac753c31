"use client";
import React, { useEffect, useRef } from "react";
import { useFormikContext } from "formik";
import { apiFunction } from "@/apiCall/function";
import { TRANSLATION } from "@/apiCall/urls/apis";
import { notification } from "@/components/loader"; // remove loaderStart/loaderEnd
import Label from "@/components/formSection/label";
import Input from "@/components/formSection/input";
import SelectForm from "@/components/formSection/selectForm";
import styles from "../styles.module.scss";

const StudentDetails = () => {
  const { values, setFieldValue } = useFormikContext();
  const debounceTimers = useRef({});

  const isValidEnglish = (text) => /^[a-zA-Z\s.,'-]*$/.test(text);
  const isValidHindi = (text) => /^[\u0900-\u097F\s.,'-]*$/.test(text);

  // Translation function WITHOUT loader
  const translateToHindi = async (text) => {
    if (!text?.trim()) return "";
    try {
      const res = await apiFunction(TRANSLATION, "POST", {
        text,
        from: "en",
        to: "hi",
      });
      if (res?.status === "success" && res.data?.data?.translatedText) {
        return res.data.data.translatedText;
      }
      notification(false, "Translation failed");
      return "";
    } catch (e) {
      console.error("Translation error:", e);
      notification(false, "Translation error");
      return "";
    }
  };

  const handleNameChange = (group, lang, value) => {
    if (lang === "english") {
      if (!isValidEnglish(value)) {
        notification(false, "Please enter only English characters");
        return;
      }
      setFieldValue(`${group}.english`, value);

      // ✅ Debounce translation without loader
      if (debounceTimers.current[group]) clearTimeout(debounceTimers.current[group]);

      debounceTimers.current[group] = setTimeout(async () => {
        const translated = value.trim() ? await translateToHindi(value) : "";
        setFieldValue(`${group}.hindi`, translated);
      }, 500);
    } else if (lang === "hindi") {
      if (!isValidHindi(value)) {
        notification(false, "Please enter only Hindi characters");
        return;
      }
      setFieldValue(`${group}.hindi`, value);
    }
  };

  const handleSimpleChange = (field, value) => setFieldValue(field, value);

  useEffect(() => {
    const timers = debounceTimers.current;
    return () => {
      Object.values(timers).forEach((t) => clearTimeout(t));
    };
  }, []);


  return (
    <div className={styles.fullWidthSection}>
      {/* Student Name */}
      <div className={styles.section}>
        <Label label="1. STUDENT NAME" required />
        <Input
          name="studentName.english"
          placeholder="English"
          value={values.studentName.english}
          onChange={(e) =>
            handleNameChange("studentName", "english", e.target.value)
          }
        />
        <Input
          name="studentName.hindi"
          placeholder="Hindi"
          value={values.studentName.hindi}
          onChange={(e) =>
            handleNameChange("studentName", "hindi", e.target.value)
          }
        />
      </div>

      {/* Father Name */}
      <div className={styles.section}>
        <Label label="2. FATHER'S NAME" required />
        <Input
          name="fatherName.english"
          placeholder="English"
          value={values.fatherName.english}
          onChange={(e) =>
            handleNameChange("fatherName", "english", e.target.value)
          }
        />
        <Input
          name="fatherName.hindi"
          placeholder="Hindi"
          value={values.fatherName.hindi}
          onChange={(e) =>
            handleNameChange("fatherName", "hindi", e.target.value)
          }
        />
      </div>

      {/* Mother Name */}
      <div className={styles.section}>
        <Label label="3. MOTHER'S NAME" required />
        <Input
          name="motherName.english"
          placeholder="English"
          value={values.motherName.english}
          onChange={(e) =>
            handleNameChange("motherName", "english", e.target.value)
          }
        />
        <Input
          name="motherName.hindi"
          placeholder="Hindi"
          value={values.motherName.hindi}
          onChange={(e) =>
            handleNameChange("motherName", "hindi", e.target.value)
          }
        />
      </div>

      {/* DOB & Gender */}
      <div className={styles.twoparts}>
        <div className={styles.section}>
          <Label label="4. DATE OF BIRTH" required />
          <Input
            type="date"
            name="dob"
            value={values.dob}
            onChange={(e) => handleSimpleChange("dob", e.target.value)}
          />
        </div>

        <div className={styles.section}>
          <Label label="5. GENDER" required />
          <SelectForm
            type="radio"
            name="gender"
            options={["Male", "Female", "Other"]}
            value={values.gender}
            onChange={(e) => handleSimpleChange("gender", e.target.value)}
          />
        </div>
      </div>

      {/* Mobile & Email */}
      <div className={styles.twoparts}>
        <div className={styles.section}>
          <Label label="6. MOBILE NO" required />
          <Input
            name="mobile"
            placeholder="Enter mobile number"
            value={values.mobile}
            maxLength={10}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, ""); 
              setFieldValue("mobile", value);
            }}
          />
        </div>
        <div className={styles.section}>
          <Label label="7. EMAIL ID" />
          <Input
            name="email"
            placeholder="Enter email address"
            value={values.email}
            onChange={(e) => handleSimpleChange("email", e.target.value)}
          />
        </div>
      </div>

      {/* Category */}
      <div className={styles.section}>
        <Label label="8. CATEGORY" required />
        <SelectForm
          type="radio"
          name="caste"
          options={["General", "EWS", "BC", "EBC", "SC", "ST"]}
          value={values.caste}
          onChange={(e) => handleSimpleChange("caste", e.target.value)}
        />
      </div>
    </div>
  );
};

export default StudentDetails;
