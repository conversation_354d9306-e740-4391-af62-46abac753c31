import React from "react";
import PropTypes from "prop-types";
import classes from "./styles.module.scss";
import Image from "next/image";

const GalleryRemoveCard = ({
  imageSrc,
  title,
  date,
  onRemove,
  imageAlt = "Gallery image",
}) => {
  return (
    <div className={classes.gallery_remove_card}>
      <div className={classes.image_container}>
        <Image
          src={imageSrc}
          alt={imageAlt}
          className={classes.card_image}
          width={300}
          height={200}
        />
      </div>

      <div className={classes.card_content}>
        <h3 className={classes.card_title}>{title}</h3>
        <p className={classes.card_date}>{date}</p>

        <button
          className={classes.remove_button}
          onClick={onRemove}
          type="button"
        >
          Remove
        </button>
      </div>
    </div>
  );
};

GalleryRemoveCard.propTypes = {
  imageSrc: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
    .isRequired,
  title: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
  onRemove: PropTypes.func.isRequired,
  imageAlt: PropTypes.string,
};

export default GalleryRemoveCard;
