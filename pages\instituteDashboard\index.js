"use client";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/router";
import InstitutePanel from "@/container/instituteDashboardContainer/institutePanel";

const InstituteDashboard = () => {
  const router = useRouter();
  const { user, instituteDetails, permissions, token } = useSelector(
    (state) => state.auth
  );

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (isClient && !token) {
      router.push("/login");
    }
  }, [isClient, token, router]);

  if (!isClient) return null;

  return (
    <InstitutePanel
      user={user}
      instituteDetails={instituteDetails}
      permissions={permissions}
    />
  );
};

export default InstituteDashboard;
