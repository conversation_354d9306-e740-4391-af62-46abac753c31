"use client";
import React, { useState } from "react";
import styles from "./styles.module.scss";
import { FiUpload } from "react-icons/fi";
import GalleryRemoveCard from "@/components/admin/galleryRemoveCard";
import Pagination from "@/components/admin/pagination";
import FileUploadModal from "@/components/admin/fileupload";
import { toast } from "react-toastify";
import {
  useAddGalleryMutation,
  useGetGalleryQuery,
  useDeleteGalleryMutation,
} from "@/injectEndpoints/adminendpoint";

export default function AdminGalleryContainer() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;
  const [isModalVisible, setModalVisible] = useState(false);

  const [addGallery] = useAddGalleryMutation();
  const [deleteGallery] = useDeleteGalleryMutation();
  const {
    data: galleryData,
    refetch: refetchGallery,
    isLoading: isFetchingGallery,
    error: galleryError,
  } = useGetGalleryQuery({
    programCode: "LISTGALLERYDETAILS",
    galleryCategoryId: "%%",
  });

  const openFileupload = () => setModalVisible(true);
  const handleCloseModal = () => setModalVisible(false);

  // Input configurations for gallery images
  const galleryInputConfigs = [
    {
      label: "Image Title:",
      type: "text",
      name: "notificationText",
      placeholder: "Enter Image Title",
      required: true,
    },
    {
      label: "Image Description:",
      type: "text",
      name: "advertisementNumber",
      placeholder: "Enter Image Description",
      required: false,
    },
  ];

  const handleRemoveImage = async (imageId) => {
    try {
      await deleteGallery({
        programCode: "DELETEGALLERYDETAILS",
        id: imageId,
      }).unwrap();

      toast.success("Image removed successfully!", {
        position: "top-center",
      });

      // Refetch gallery data to update the list
      await refetchGallery();
    } catch (err) {
      toast.error("Failed to remove image: " + err.message, {
        position: "top-center",
      });
    }
  };

  const handleAddImage = async (formData) => {
    try {
      await addGallery({
        programCode: "ADDGALLERYDETAILS",
        galleryCategoryId: 1,
        title: formData.notificationText,
        description: formData.advertisementNumber,
        imageUrl: formData.notificationFileUrl,
        galleryCategoryId: 1,
      }).unwrap();

      toast.success("Image uploaded successfully!", {
        position: "top-center",
      });

      // Refetch gallery data to update the list
      await refetchGallery();
    } catch (err) {
      toast.error("Failed to upload image: " + err.message, {
        position: "top-center",
      });
    } finally {
      handleCloseModal();
    }
  };

  // Get gallery images from API
  const galleryImages = galleryData?.data?.data || [];

  // Pagination logic
  const totalItems = galleryImages.length;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentImages = galleryImages.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  if (isFetchingGallery) {
    return <div>Loading gallery images...</div>;
  }

  if (galleryError) {
    return <div>Error loading gallery: {galleryError.message}</div>;
  }

  return (
    <div className={styles.notifications}>
      <div className={styles.header}>
        <h3 className={styles.title}>Remove/Upload Gallery Images:</h3>
        <button className={styles.addBtn} onClick={openFileupload}>
          <FiUpload /> &nbsp; Upload New
        </button>
      </div>

      <div className={styles.gallery_grid}>
        {currentImages.length > 0 ? (
          currentImages.map((image) => (
            <GalleryRemoveCard
              key={image.id}
              imageSrc={image.imageUrl || image.src}
              title={image.title}
              date={image.createdAt}
              onRemove={() => handleRemoveImage(image.id)}
              imageAlt={image.title}
            />
          ))
        ) : (
          <p className={styles.empty}>No gallery images found.</p>
        )}
      </div>

      <FileUploadModal
        advertisementText="You can upload image files (JPG, PNG, WEBP)."
        onClose={handleCloseModal}
        onSubmit={handleAddImage}
        isVisible={isModalVisible}
        inputConfigs={galleryInputConfigs}
      />

      <Pagination
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
      />
    </div>
  );
}
