"use client";
import React from "react";
import PropTypes from "prop-types";
import styles from "./styles.module.scss";

const FailureModal = ({ txnid }) => {
  const handleRetry = () => {
    window.location.href = "/instituteDashboard/registration";
  };

  const handleDashboard = () => {
    window.location.href = "/instituteDashboard";
  };

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <div className={styles.iconWrapper}>
          <div className={styles.outerCircle}>
            <div className={styles.crossmark}>✖</div>
          </div>
        </div>

        <h2>Payment Failed!</h2>
        <p>Something went wrong with your transaction. Please try again or contact support.</p>

        {txnid && (
          <p>
            <strong>Transaction ID:</strong> {txnid}
          </p>
        )}

        <div className={styles.actions}>
          <button className={styles.outlineBtn} onClick={handleRetry}>
            Try Again
          </button>
          <button className={styles.fillBtn} onClick={handleDashboard}>
            Go to Dashboard
          </button>
        </div>
      </div>
    </div>
  );
};

FailureModal.propTypes = {
  txnid: PropTypes.string,
};

export default FailureModal;
