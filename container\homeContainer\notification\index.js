import classes from "./styles.module.scss";
import Image from "next/image";

const Notification = () => {
    return(
        <div className={classes.notification} id="notification">
            <div className={classes.notification_content}>

                <div className={classes.notification_box}>
                    <div className={classes.notification_header}>
                        <p className={classes.header_text}>अधिसूचना</p>
                    </div>
                    <div className={classes.notification_body}>
                        {/* <ul className={classes.notification_list}>
                            <li>
                                एकीकृत शिक्षक शिक्षा कार्यक्रम (ITEP) बीए. बीएड. हेतु प्रवेश पंजीकरण का अंतिम अवसर <span className={classes.date}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/calender.webp`} width={48} height={48} alt="icon" className={classes.date_icon} /> 21/07/2025
                                </span>     
                            </li>
                            <li>
                                शिक्षाशास्त्री (B.Ed) पाठ्यक्रम हेतु आवेदनकर्ताओं के लिए समर्थ पोर्टल पर सुधार (Correction) की सूचना <span className={classes.date}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/calender.webp`} width={48} height={48} alt="icon" className={classes.date_icon} /> 21/07/2025
                                </span>
                            </li>
                            <li>
                                डीन, स्कूल ऑफ कंटेम्पररी नॉलेज सिस्टम्स एवं ह्यूमैनिटीज से संबंधित अधिसूचना <span className={classes.date}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/calender.webp`} width={48} height={48} alt="icon" className={classes.date_icon} /> 21/07/2025
                                </span>
                            </li>
                            <li>
                                आवासीय संस्कृत शिक्षक प्रशिक्षण वर्गः 2025 हेतु सूचना <span className={classes.date}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/calender.webp`} width={48} height={48} alt="icon" className={classes.date_icon} /> 21/07/2025
                                </span>
                            </li>
                        </ul> */}
                    </div>
                </div>

            </div>
        </div>
    )
}

export default Notification;