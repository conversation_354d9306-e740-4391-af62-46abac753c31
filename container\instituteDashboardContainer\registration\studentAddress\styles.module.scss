$primary: #571f0b;

.twoColumnLayout {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 12px 16px;
  background-color: transparent;
  margin-bottom: 16px;
  width: 100%;
}

.addressGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.inputContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.radioGroup {
  display: flex;
  gap: 2rem;
  margin: 1rem 0;
  align-items: center;
}

.radioOption {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.fullWidth {
  width: 100%;
}
