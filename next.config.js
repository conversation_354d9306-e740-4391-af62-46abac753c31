/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "prod-1.static.codebuckets.in",
        pathname: "/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/**",
      },
      {
        protocol: "https",
        hostname: "bihar-sanskrit-siksha-board.codebucketstage.online",
        pathname: "/api/files/bssb/**",
      },
      {
        protocol: "https",
        hostname: "bssb.bihar.gov.in",
        pathname: "/api/files/bssb/**",
      },
    ],
  },

  output: "standalone",

  async headers() {
    return [
      {
        source: "/_next/image",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
