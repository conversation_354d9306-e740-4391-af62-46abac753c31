@import "../../assets/css/global.scss";

.choose_language {
  position: relative;

  .choose_lang_btn {
    border: 1px solid #c26c31;
    background-color: #3b1406;
    border-radius: 6px;
    padding: 4px 10px 4px 20px;
    color: white;
    font-size: 13px;
    font-family: $poppinsRegular400;
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
      opacity: 0.7;
    }
  }

  .lang_option {
    position: absolute;
    top: calc(100% + 4px);
    width: 100%;
    border: 1px solid #c26c31;
    background-color: #3b1406;
    border-radius: 6px;

    .nav_btn,
    .lang_btn {
      display: block;
      width: 100%;
      border-radius: 5px;
      font-family: $poppinsRegular400;
      font-size: 13px;
      padding: 4px 10px;
      background-color: transparent;
      border: unset;
      color: white;
      cursor: pointer;

      &:hover {
        background-color: #c26c31;
      }
    }
  }
}