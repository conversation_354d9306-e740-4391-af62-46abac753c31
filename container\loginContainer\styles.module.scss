@import "../../assets/css/global.scss";

.login_container{
    width: 100%;
    height: auto;
    padding: 60px 15px 130px;

    .login_content{
        width: 100%;
        height: auto;

        .login_header{
            color: $darkBrown;
            font-family: $poppinsSemiBold600;
            font-size: 28px;
            text-align: center;
        }

        .sub_header{
            color: $darkBrown;
            font-family: $poppinsRegular400;
            font-size: 17px;
            text-align: center;
            margin-top: 5px;
        }

        .login_form{
            width: 460px;
            height: auto;
            margin: 60px auto 0px;

            .form_header{
                width: 100%;
                height: auto;
                padding: 12px 42px;
                background-color: $reddishBrown;
                border-radius: 12px 12px 0px 0px;
                display: flex;
                align-items: center;
                justify-content: center;

                .tab_btn{
                    border: unset;
                    background-color: unset;
                    color: $white;
                    font-size: 18px;
                    font-family: $poppinsRegular400;
                    border-radius: 6px;
                    padding: 8px 24px;
                    cursor: pointer;
                }

                .active_tab{
                    background-color: $white;
                    color: $reddishBrown;
                    font-family: $poppinsSemiBold600;
                }
            }

            .form_body{
                width: 100%;
                height: auto;
                padding: 24px 28px;
                border: 6px solid $borderOne;
                border-top: unset;
                border-radius: 0px 0px 12px 12px;

                .label{
                    color: $textBlack;
                    font-size: 12px;
                    font-family: $poppinsRegular400;
                    margin-bottom: 20px;
                }

                .form{
                    width: 100%;
                    height: auto;

                    .remember_me{
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 20px;

                        .remember_checkbox{
                            display: flex;
                            align-items: center;
                            font-size: 14px;
                            font-family: $interMedium500;

                            input[type="checkbox"]{
                                accent-color: $reddishBrown;
                                margin-right: 5px;
                            }
                        }

                        .forget_password_btn{
                            border: unset;
                            background-color: unset;
                            color: $textBlackTwo;
                            text-decoration: underline;
                            font-size: 14px;
                            font-family: $interMedium500;
                            cursor: pointer;

                            &:hover{
                                opacity: 0.7;
                            }
                        }
                    }
                }

                .terms_conditions{
                    border: 1px solid #E2E8F0;
                    border-radius: 6px;
                    padding: 8px 16px;
                    margin-top: 20px;

                    .terms_conditions_text{
                        text-align: center;
                        color: #0F172A;
                        font-family: $interMedium500;
                        font-size: 14px;
                    }
                }
            }
        }
    }
}