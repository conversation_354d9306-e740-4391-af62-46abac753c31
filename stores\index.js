import { configureStore } from "@reduxjs/toolkit";
import rootReducer from "./rootReducer";
import { apiSlice } from "@/apiSlice/apiSlice";

const PERSIST_KEY = "bihar_sanskrit_shiksha_board";

// Load persisted state from localStorage
const loadState = () => {
  if (typeof window === "undefined") return undefined;
  try {
    const serializedState = localStorage.getItem(PERSIST_KEY);
    return serializedState ? JSON.parse(serializedState) : undefined;
  } catch (err) {
    console.warn("Invalid JSON in localStorage:", err);
    localStorage.removeItem(PERSIST_KEY);
    return undefined;
  }
};

// Save only safe slices of state into localStorage
const saveState = (state) => {
  if (typeof window === "undefined") return;
  try {
    const safeState = {
      form: state.form, // keep form state
      auth: {
        user: state.auth.user,
        permissions: state.auth.permissions,
        instituteDetails: state.auth.instituteDetails,
        token: state.auth.token,
        educationLevelStatus: state.auth.educationLevelStatus, // ✅ added
      },
    };
    localStorage.setItem(PERSIST_KEY, JSON.stringify(safeState));
  } catch (err) {
    console.error("Redux save error:", err);
  }
};

const preloadedState = loadState();

const store = configureStore({
  reducer: rootReducer,
  preloadedState,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: false }).concat(apiSlice.middleware),
});

// Persist on every state update
if (typeof window !== "undefined") {
  store.subscribe(() => saveState(store.getState()));
}

export default store;
