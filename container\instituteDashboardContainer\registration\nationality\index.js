"use client";
import React from "react";
import { useFormikContext } from "formik";
import Label from "@/components/formSection/label";
import Input from "@/components/formSection/input";
import SelectForm from "@/components/formSection/selectForm";
import styles from "../styles.module.scss";

const NationalitySection = () => {
  const { values, setFieldValue } = useFormikContext();

  return (
    <>
      <div>
        <SelectForm
          type="radio"
          name="nationality"
          options={["Indian", "Others"]}
          value={values.nationality}
          onChange={(e) => setFieldValue("nationality", e.target.value)}
        />
      </div>

      {values.nationality === "Indian" && (
        <div className={styles.section}>
          <Label label="Aadhaar No :" required />
          <Input
            name="aadhaar"
            placeholder="Enter Aadhaar number"
            value={values.aadhaar}
            maxLength={12} 
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, ""); 
              setFieldValue("aadhaar", value);
            }}
          />
        </div>
      )}


      {values.nationality === "Others" && (
        <div className={styles.section}>
          <div className={styles.sectionnationality}>
            <Label label="Country Name :" required />
            <Input
              name="country"
              placeholder="Enter your country"
              value={values.country}
              onChange={(e) => setFieldValue("country", e.target.value)}
            />
          </div>

          <div className={styles.sectionnationality}>
            <Label label="ID Type :"  required/>
            <Input
              name="foreignIdType"
              placeholder="e.g. National ID, Residence Permit, SSN"
              value={values.foreignIdType}
              onChange={(e) => setFieldValue("foreignIdType", e.target.value)}
            />
          </div>

          <div className={styles.sectionnationality}>
            <Label label="ID Number :"  required/>
            <Input
              name="foreignIdNumber"
              placeholder="Enter ID number"
              value={values.foreignIdNumber}
              onChange={(e) => setFieldValue("foreignIdNumber", e.target.value)}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default NationalitySection;
