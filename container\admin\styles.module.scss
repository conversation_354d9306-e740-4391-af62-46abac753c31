$page-bg: #f8f1e6;
$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.greeting {
  display: flex;
  flex-direction: column;

  h2 {
    font-size: 22px;
    font-weight: bolder;
    color: $primary;
    margin: 0 0 4px 0;
  }

  p {
    font-size: 14px;
    font-weight: 700;
    color: $primary;
    margin: 0;
  }
}
.layout {
  display: flex;
  min-height: 100vh;
}

.mainContent {
  flex: 1;
  padding: 50px;
  border: 1px solid $primary;
  border-radius: 8px;
  margin: 55px;
}

.dashboard {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.searchBar {
  position: relative;
  flex: 1;
  max-width: 400px;
  margin-left: 20px;

  .searchIcon {
    position: absolute;
    left: 342px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: $primary;
    pointer-events: none;
    transition: opacity 0.2s ease;
  }

  .hidden {
    opacity: 0;
  }

  input {
    flex: 1;
    width: 100%;
    padding: 10px 14px 10px 36px;
    border: 1px solid $primary;
    border-radius: 16px;
    font-size: 14px;
    background: transparent;
    color: $primary;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba(87, 31, 11, 0.2);
    }
  }
}
