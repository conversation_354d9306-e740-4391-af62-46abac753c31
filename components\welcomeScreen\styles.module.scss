@import "../../assets/css/global.scss";

.rxWorld {
	width: 100%;
}

.rnOuter {
	background: $burntCoffee;
	overflow: hidden;
	position: relative;
	height: 100vh;
}

.btn,
.btnNext {
	cursor: pointer;
	padding: 8px 20px;
	border: none;
	border-radius: 3px;
	z-index: 999999;
	position: fixed;
	bottom: 5em;
	left: 50%;
	transform: translateX(-50%);
	font-family: $interMedium500;
}

.btn {
	font-size: 24px;
	color: $reddishBrown;
}

@keyframes zoomIn {
	0% {
		transform: scale(0.1);
		opacity: 0;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.btnNext {
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	width: 100%;
	height: 100dvh;
	left: 0;
	top: 0;
	background-image: url("../../assets/images/welcomeTwo.webp");
	background-repeat: no-repeat;
	background-size: 80% 100%;
	background-position: center;
	transform-origin: center;

	animation: zoomIn 0.8s ease forwards;
	animation-delay: 2s;
	opacity: 0;

	.text_wrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;

		.welcome_img,
		.welcome_img_two {
			width: 30vw;
			height: auto;
			pointer-events: none; // prevent images from blocking clicks
		}

		.welcome_img_two {
			transform: rotate(180deg);
			margin-left: 15px;
		}

		.animated_gold_text {
			all: unset;
			box-sizing: border-box;
			font-size: 1.2vw;
			font-family: $interThin100;
			font-weight: 800;
			cursor: pointer;
			position: relative; // required for z-index
			z-index: 10; // make button clickable
			pointer-events: auto;
			background: linear-gradient(
				120deg,
				#FFD700 0%,
				#FFA500 20%,
				#FFFACD 40%,
				#FFA500 60%,
				#FFD700 80%
			);
			background-size: 200% auto;
			background-clip: text;
			-webkit-background-clip: text;
			color: transparent;
			-webkit-text-fill-color: transparent;
			animation: goldShimmer 4.5s ease-in-out infinite;
			border: 1px solid gold ;
			border-radius: 10px;
			padding: 5px 25px;
		}
	}
}

@keyframes goldShimmer {
	0% {
		background-position: -150% 0;
	}
	100% {
		background-position: 150% 0;
	}
}

.rnInner {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	display: flex;
	z-index: 10;
	overflow: hidden;
}

.rnLeft,
.rnRight {
	width: 50%;
	height: 100%;
	display: flex;
	align-items: center;
	transition: transform 2s ease;
	overflow: hidden;
}

.rnLeft {
	justify-content: flex-end;
}

.rnRight {
	justify-content: flex-start;
}

.openLeft {
	transform: translateX(-100%);
}

.openRight {
	transform: translateX(100%);
}

.rnUnit {
	width: 5vw;
	height: 120vh;
	background: repeating-linear-gradient(
		to left,
		$burntCoffee 4vw,
		$darkBrown 8vw,
		$reddishBrown 10vw
	);
	background-size: 100% 100%;
	display: inline-block;
	transform-origin: 0 0%;
	transform: rotate(3deg);
	animation: rnUnit 3s ease infinite;
}

@keyframes rnUnit {
	50% {
		transform: rotate(-3deg);
	}
}

.aoTable {
	display: table;
	width: 100%;
	height: 100vh;
	text-align: center;
}

.aoTableCell {
	color: red;
	display: table-cell;
	vertical-align: middle;
	transition: color 3s ease;
}