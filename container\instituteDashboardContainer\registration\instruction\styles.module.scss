$page-bg: #f8f1e6;
$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;

.pageWrapper {
  width: 100%;
  max-width: 1200px;
  min-height: 100vh;
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin: 0 auto;
}

.container {
  width: 100%;
  padding: 30px;
  border-radius: 8px;
  font-family: $font-family;
  color: $primary;
  line-height: 1.6;
  background: transparent;

  h2 {
    text-align: center;
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 50px;
    color: $primary;
    text-decoration: underline;
    text-decoration-color: $primary;
    text-underline-offset: 10px;

    span {
      font-weight: 600;
      font-family: "Noto Sans Devanagari", serif;
    }
  }
}



.instructionsList {
  list-style-type: none;
  padding-left: 0;
  font-size: 16px;
  line-height: 1.8;
  color: $primary;


  li {
    margin-bottom: 12px;

  }
}

.goBackButton {
  background-color: $primary;
  color: white;
  padding: 12px 30px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 30px;
  width: 100%;
}

.nameBox {
  margin-top: 20px;
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: wrap;
}

.letterBox {
  border: 2px solid $primary;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  background: white;
}

.exampleText {
  display: inline-block;
  letter-spacing: 0.5em;
  font-weight: bold;
  font-size: 1.2rem;
  color: #2c3e50;
}

/* Responsive Styles for smaller devices */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }



  .instructionsList {
    font-size: 14px;
  }

  .goBackButton {
    width: 100%; // Ensure button is full width on small screens
    padding: 10px 20px; // Adjust padding for smaller screens
  }

  .letterBox {
    width: 30px; // Reduce size of letter boxes on smaller screens
    height: 30px;
    font-size: 14px;
  }
}