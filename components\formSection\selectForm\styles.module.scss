.inlineGroup {
  display: flex;
  align-items: center;
  gap: 0px;
  flex-wrap: wrap;
}

.inlineLabel {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-color, #571f0b);
  white-space: nowrap;
  width: auto;
}

.inlineOptions {
  display: flex;
  gap: 60px;
  flex-wrap: wrap;
  flex-grow: 1;
  width: 100%;
}

.optionItem {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 1rem;
  color: var(--text-color, #571f0b);
  cursor: pointer;
}

.optionItem input {
  cursor: pointer;
}

.selectContainer {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
}

.selectInput {
  padding: 8px 12px;
  font-size: 1rem;
  color: var(--text-color, #571f0b);
  background-color: transparent;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  appearance: none;
  transition: border-color 0.3s ease;
  width: 100%;
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 10px;
}

.selectInput:focus {
  border-color: #571f0b;
  outline: none;
}

.selectInput:hover {
  border-color: #888;
}

.selectInput option {
  padding: 8px;
  font-size: 1rem;
}

.inputField {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
  color: var(--text-color, #571f0b);
  background-color: transparent;
  transition: border-color 0.3s ease;
}

.inputField:focus {
  border-color: #571f0b;
  outline: none;
}

.inputField:hover {
  border-color: #888;
}

.error {
  color: red;
  font-size: 0.875rem;
}

/* Responsive Design */

/* For screens ≤ 1024px (Tablets) */
@media (max-width: 1024px) {
  .inlineOptions {
    gap: 20px; /* Reduce gap between inline options on tablet */
  }

  .selectContainer {
    margin-bottom: 15px; /* Slightly reduce margin */
  }

  .selectInput,
  .inputField {
    font-size: 0.95rem; /* Adjust font size for smaller screens */
    padding: 8px 10px; /* Reduce padding for a more compact design */
  }

  .inlineLabel {
    font-size: 0.95rem; /* Slightly reduce label font size */
  }

  .optionItem {
    font-size: 0.95rem; /* Reduce font size for better fit */
  }
}

/* For screens ≤ 768px (Mobile/Tablets) */
@media (max-width: 768px) {
  .inlineGroup {
    flex-direction: column; /* Stack inline elements vertically on smaller screens */
    align-items: flex-start; /* Align items to the start */
  }

  .inlineOptions {
    gap: 15px; /* Reduce gap */
    width: 100%; /* Ensure options take full width */
  }

  .selectContainer {
    margin-bottom: 12px; /* Reduce margin for mobile devices */
  }

  .selectInput,
  .inputField {
    font-size: 0.9rem; /* Further reduce font size */
    padding: 8px 10px; /* Smaller padding */
  }

  .inlineLabel {
    font-size: 0.9rem; /* Reduce label font size for mobile */
  }

  .optionItem {
    font-size: 0.9rem; /* Adjust font size for mobile screens */
  }
}

@media (max-width: 480px) {
  .inlineGroup {
    flex-direction: column; /* Stack inline elements vertically */
    align-items: flex-start;
  }

  .inlineOptions {
    gap: 10px; /* Further reduce gap */
  }

  .selectInput,
  .inputField {
    font-size: 0.875rem; /* Decrease font size even more for small screens */
    padding: 8px; /* Even smaller padding */
  }

  .inlineLabel {
    font-size: 0.875rem; /* Adjust label font size */
  }

  .optionItem {
    font-size: 0.875rem; /* Reduce font size */
  }

  .selectInput {
    padding-right: 30px; /* Ensure select input has space for the dropdown indicator */
  }
}
