@import "../../assets/css/global.scss";

.institutions_container{
    width: 100%;
    height: auto;
    padding: 50px 120px;

    .institutions_content{
        width: 100%;
        height: auto;

        .institutions_header{
            color: #571F0B;
            font-size: 24px;
            font-family: $poppinsSemiBold600;
            border-bottom: 2px solid #571F0B;
            padding-bottom: 8px;
            margin-bottom: 20px;
            display: inline-block;
        }

        .pagination{
            margin-top: 40px;
            display: flex;
            justify-content: center;

            .pagination_custom {
                font-size: 24px;

                // Optional: scope to just page numbers
                .MuiPaginationItem-page {
                    font-size: 24px;
                }
            }
        }
    }
}


.search_bar {
    display: flex;
    justify-content: flex-end;
    margin: 20px 0;
    padding: 10px;
    color: #571F0B;
    gap:20px;

    input {
        color: #571F0B;
         border: 1px solid #571F0B;
            border-radius: 4px;
    } 
}
