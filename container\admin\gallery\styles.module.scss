@import "../../../assets/css/global.scss";
$page-bg: #f8f1e6;
$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;
.notifications {
  background: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 17px;
      color: $primary;
      font-weight: bolder;
    }

    .addBtn {
      background: $primary;
      color: #fff;
      border: none;
      padding: 8px 14px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;

      font-weight: 500;
      transition: background 0.2s ease;

      &:hover {
        background: darken($primary, 5%);
      }
    }
  }

  .gallery_grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 10px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 16px;

    .gallery_grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}
