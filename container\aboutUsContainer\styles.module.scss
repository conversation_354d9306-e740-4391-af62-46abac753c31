@import "../../assets/css/global.scss";

.about_us_container{
    width: 100%;
    height: auto;
    padding: 50px 120px;

    .about_us_content{
        width: 100%;
        height: auto;

        .section_one{
            width: 100%;
            height: auto;
            display: flex;

            .section_one_left, .section_one_right{
                width: 50%;
                height: auto;

                .about_us_header{
                    color: #571F0B;
                    font-size: 24px;
                    font-family: $poppinsSemiBold600;
                    border-bottom: 2px solid #571F0B;
                    padding-bottom: 8px;
                    margin-bottom: 20px;
                    display: inline-block;
                }

                .big_header{
                    color: #571F0B;
                    font-size: 36px;
                    font-family: $poppinsSemiBold600;
                    margin-bottom: 30px;
                }
                
                .desc{
                    color: #571F0B;
                    font-size: 16px;
                    font-family: $poppinsLight300;
                }

                .learn_more{
                    background-color: #571F0B;
                    padding: 8px 36px;
                    border-radius: 6px;
                    border: unset;
                    margin-top: 30px;
                    color: white;
                    font-size: 14px;
                    font-family: $poppinsRegular400;
                    cursor: pointer;
                }
            }

            .section_one_right{
                position: relative;
                z-index: 9;

                .bg_one{
                    position: absolute;
                    left: -1px;
                    top: -1px;
                    width: 30%;
                    height: 100%;
                    background-color: #571F0B;
                }

                .bg_two{
                    position: absolute;
                    right: -1px;
                    bottom: -1px;
                    width: 30%;
                    height: 100%;
                    background-color: #571F0B;
                }

                .right_image{
                    width: 100%;
                    height: auto;
                    position: relative;
                    background-color: white;
                    padding: 30px;
                    z-index: 1;

                    img{
                        width: 100%;
                        height: 100%;
                        margin-top: -40px;
                    }
                }
            }
        }

        .section_two{
            width: 100%;
            height: auto;
            margin-top: 70px;

            .mission_vision_card{
                width: 100%;
                height: auto;
                border: 1px solid #571F0B;
                border-radius: 12px;
                padding: 40px 68px;
                display: flex;
                align-items: start;
                gap: 40px;
                margin-bottom: 60px;

                .msn_vsn_img{
                    width: 90px;
                    height: auto;
                }

                .right_text{
                    .right_text_header{
                        color: #571F0B;
                        font-size: 36px;
                        font-family: $poppinsSemiBold600;
                    }

                    .right_text_desc{
                        font-size: 16px;
                        color: #571F0B;
                        font-family: $poppinsLight300;
                    }
                }
            }
        }

        .section_three{
            width: 100%;
            height: auto;

            .policies_header{
                position: relative;
                margin-bottom: 40px;

                &::before{
                    content: '';
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 97%;
                    height: 2px;
                    background-color: #571F0B;
                }

                p{
                    background-color: #571F0B;
                    border-radius: 12px 12px 0px 12px;
                    color: white;
                    padding: 8px 50px;
                    font-size: 26px;
                    font-family: $poppinsMedium500;
                    display: inline-block;
                }
            }

            .policies_card{
                width: 100%;
                height: auto;
                border: 1px solid #571F0B;
                border-radius: 6px;
                padding: 12px 24px;
                margin-bottom: 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 40px;
                text-decoration: unset;

                .left_part{
                    display: flex;
                    align-items: center;
                    gap: 30px;

                    .policies_card_header{
                        font-size: 20px;
                        color: #571F0B;
                        font-family: $poppinsRegular400;
                    }
                }

                .right_arrow{
                    cursor: pointer;
                }
            }
        }
    }
}