"use client";
import React from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { Formik, Form } from "formik";
import { toast } from "react-toastify";

import GeoTaggedPhotos from "./geoTaggedPhotos";
import InstituteContacts from "./instituteContacts";

import {
  useGetInstituteContactsQuery,
  useAddInstituteContactMutation,
  useUpdateInstituteContactMutation,
  useUploadInstitutePictureMutation,
  useListInstitutePicturesQuery,
  useDeleteInstitutePictureMutation,
  useGetNodalSchoolsDropdownQuery,
} from "@/injectEndpoints/institutedetailsendpoint";

import styles from "./styles.module.scss";
import { FaUniversity } from "react-icons/fa";

const InstitutePanel = () => {
  const router = useRouter();
  const { user, instituteDetails, educationLevelStatus } = useSelector(
    (state) => state.auth
  );

  const district = instituteDetails?.district || "";

  const { data: contactsData, refetch: refetchContacts } = useGetInstituteContactsQuery();
  const [addContact] = useAddInstituteContactMutation();
  const [updateContact] = useUpdateInstituteContactMutation();

  const showNodalSchool = instituteDetails?.typeOfSchool === "प्रस्तावित";

  const { data: nodalSchoolsData = [] } = useGetNodalSchoolsDropdownQuery(district, {
    skip: !district || !showNodalSchool,
  });
  const nodalSchools = showNodalSchool ? nodalSchoolsData : [];

  const { data: uploadedPhotos = [], refetch: refetchPhotos } = useListInstitutePicturesQuery();
  const [uploadInstitutePicture] = useUploadInstitutePictureMutation();
  const [deleteInstitutePicture] = useDeleteInstitutePictureMutation();

  const handleRegistrationClick = () => router.push("/instituteDashboard/registration");
  const handleViewRegistrationClick = () => router.push("/instituteDashboard/viewstudentsRegistration");

  const handleFinalSubmit = async (values, { resetForm }) => {
    try {
      const newPhotos = values.uploads.geoPhotos?.filter((p) => p.isNew);
      if (!newPhotos?.length) {
        toast.error("Please upload at least one new Geo Tagged Photo!", { position: "top-center" });
        return;
      }

      for (const photo of newPhotos) {
        await uploadInstitutePicture({
          institutePictureUrl: photo.url,
          programCode: "ADDINSTITUTEPICTURE",
        }).unwrap();
      }

      toast.success("All Geo Tagged Photos submitted successfully!", { position: "top-center" });
      resetForm();
      refetchPhotos();
    } catch (error) {
      console.error(error);
      toast.error("Submission failed, please try again.", { position: "top-center" });
    }
  };

  const showRegistrationButtons =
    (Array.isArray(educationLevelStatus) && educationLevelStatus.includes("Madhyama")) ||
    (!educationLevelStatus || (Array.isArray(educationLevelStatus) && educationLevelStatus.length === 0)) &&
    showNodalSchool;

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div>
          <h3>Welcome Back, {user?.login || "Institute"}!</h3>
          <p>Manage, update, and organize with ease</p>
        </div>

        {showRegistrationButtons && (
          <div className={styles.headerBtns}>
            <button className={styles.filledBtn} onClick={handleViewRegistrationClick}>
              View Registration
            </button>
            <button className={styles.filledBtn} onClick={handleRegistrationClick}>
              Madhyama Registration Form
            </button>
          </div>
        )}
      </div>

      <div className={styles.schoolInfo}>
        <div className={styles.schoolTitle}>
          <FaUniversity size={58} />
          <div>
            <h4>
              {instituteDetails?.instituteName || contactsData?.institute_name_eng || "Institute Name"}
            </h4>
            <p>
              {instituteDetails?.district || "District"} , {instituteDetails?.code || "कोटि"} कोटि
              {showNodalSchool && <span> , प्रस्तावित</span>}
              {instituteDetails?.typeOfSchool === "प्रस्वीकृत" && <span> , प्रस्वीकृत</span>}
            </p>
          </div>
        </div>
      </div>

      <InstituteContacts
        contactsData={contactsData}
        addContact={addContact}
        updateContact={updateContact}
        refetchContacts={refetchContacts}
        nodalSchools={nodalSchools}
      />

      <Formik initialValues={{ uploads: { geoPhotos: [] } }} onSubmit={handleFinalSubmit}>
        <Form>
          <GeoTaggedPhotos
            existingPhotos={uploadedPhotos}
            refresh={refetchPhotos}
            deletePhoto={deleteInstitutePicture}
          />
        </Form>
      </Formik>
    </div>
  );
};

export default InstitutePanel;
