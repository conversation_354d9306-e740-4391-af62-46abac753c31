"use client";
import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useFormikContext } from "formik";
import Label from "@/components/formSection/label";
import SelectForm from "@/components/formSection/selectForm";
import Input from "@/components/formSection/input";
import { FileUpload } from "@/components/formSection/fileUpload";
import styles from "../styles.module.scss";

const StudentCategory = ({ handleFileChange, handleRemove, handleView }) => {
  const { values, setFieldValue } = useFormikContext();
  const [isClient, setIsClient] = useState(false); // <-- track client-only rendering
  const category = values?.studentCategory;

  useEffect(() => {
    setIsClient(true); // mark that we're running on client
  }, []);

  // Clear dependent fields when category changes
  useEffect(() => {
    if (!isClient) return; // only run on client

    if (category !== "Ex") {
      setFieldValue("oldRegNumber", "");
      setFieldValue("regYear", "");
    }
    if (category !== "Private") {
      setFieldValue("uploads.supportingDocument", "");
    }
  }, [category, setFieldValue, isClient]);

  if (!isClient) return null; // avoid SSR mismatch

  return (
    <div>
      {/* Category Selection */}
      <div className={styles.sectioncategory}>
        <SelectForm
          type="radio"
          name="studentCategory"
          options={["Regular", "Private", "Ex"]}
        />
      </div>

      {/* Ex Category Fields */}
      {category === "Ex" && (
        <div className={styles.section}>
          <Label
            label="ENTER YOUR OLD REGISTRATION NUMBER"
            htmlFor="oldRegNumber"
          />
          <Input
            name="oldRegNumber"
            placeholder="Enter old registration number"
            value={values.oldRegNumber || ""}
            onChange={(e) => setFieldValue("oldRegNumber", e.target.value)}
          />

          <Label label="ENTER REGISTRATION YEAR" htmlFor="regYear" required />
          <Input
            name="regYear"
            placeholder="Enter registration year"
            value={values.regYear || ""}
            onChange={(e) => setFieldValue("regYear", e.target.value)}
          />
        </div>
      )}

      {/* Private Category File Upload */}
      {category === "Private" && (
        <div className={styles.section}>
          <Label
            label="ATTACH AFFIDAVIT / NOC / OTHER SUPPORTING DOCUMENT"
            htmlFor="uploads.supportingDocument" required
          />
          <FileUpload
            type="document"
            label="Affidavit"
            name="uploads.supportingDocument"
            value={values.uploads?.supportingDocument || ""}
            onFileChange={(e) =>
              handleFileChange("uploads.supportingDocument")(e, setFieldValue)
            }
            onRemove={() =>
              handleRemove("uploads.supportingDocument", setFieldValue)
            }
            onView={() =>
              handleView(values.uploads?.supportingDocument || "")
            }
          />
        </div>
      )}
    </div>
  );
};

StudentCategory.propTypes = {
  handleFileChange: PropTypes.func.isRequired,
  handleRemove: PropTypes.func.isRequired,
  handleView: PropTypes.func.isRequired,
};

export default StudentCategory;
