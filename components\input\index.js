import React from "react";
import PropTypes from "prop-types";
import classes from "./styles.module.scss";

const Input = ({
    label,
    type,
    name,
    value,
    placeholder,
    onChange,
    className,
    ...props
}) => {
    return (
        <div className={classes.input_component}>
            {label && <label className={classes.label} htmlFor={name}>{label}</label>}
            <input
                className={classes.custom_input}
                type={type}
                id={name}
                name={name}
                value={value}
                placeholder={placeholder}
                onChange={onChange}
                {...props}
            />
        </div>
    );
};

Input.propTypes = {
    label: PropTypes.string,
    type: PropTypes.string,
    name: PropTypes.string.isRequired,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    placeholder: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    className: PropTypes.string,
};

Input.defaultProps = {
    type: "text",
    label: "",
    placeholder: "",
    className: "",
};

export default Input;
