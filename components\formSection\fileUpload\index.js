"use client";
import React, { useRef } from "react";
import { FaUpload, FaReceipt, FaUserCircle, FaTimes } from "react-icons/fa";
import PropTypes from "prop-types";
import Image from "next/image";
import { toast } from "react-toastify";
import { useUploadFileMutation } from "@/injectEndpoints/studentRegistrationendpoint";
import { useFormikContext } from "formik";
import styles from "./styles.module.scss";

const FileUpload = ({ label, name, type = "general", value, allowedTypes }) => {
  const fileInputRef = useRef(null);
  const [uploadFile] = useUploadFileMutation();
  const { setFieldValue } = useFormikContext();

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const defaultTypeMap = {
    photo: ["image/jpeg", "image/png"],
    receipt: ["application/pdf"],
    document: ["application/pdf"],
    general: ["image/jpeg", "image/png"],
  };

  const acceptedTypes =
    allowedTypes || defaultTypeMap[type] || defaultTypeMap.general;

  const handleChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    if (!acceptedTypes.includes(file.type)) {
      toast.error("केवल अनुमत फाइलें अपलोड करें (JPG, PNG या PDF)", {
        position: "top-center",
      });
      return;
    }

    try {
      const res = await uploadFile({ uploadType: name, file }).unwrap();
      const fileUrl = res?.data?.data?.url;
      if (!fileUrl) throw new Error("No file URL returned");

      setFieldValue(name, fileUrl);
      toast.success(res?.data?.data?.message || `${label} uploaded successfully`, {
        position: "top-center",
        autoClose: 1500,
      });
    } catch (err) {
      console.error(err);
      toast.error(`${label} upload failed: ${err.message || "Unknown error"}`, {
        position: "top-center",
        autoClose: 2000,
      });
      setFieldValue(name, "");
    }

    event.target.value = "";
  };

  const handleRemove = () => {
    setFieldValue(name, "");
  };

  const handleView = () => {
    if (!value) {
      toast.error("फ़ाइल उपलब्ध नहीं है", { position: "top-center" });
      return;
    }
    window.open(value, "_blank");
  };

  let icon = <FaUpload className={styles.uploadIcon} />;
  if (type === "receipt") icon = <FaReceipt className={styles.uploadIcon} />;
  else if (type === "photo") icon = <FaUserCircle className={styles.uploadIcon} />;

  let fileName = "";
  if (typeof value === "string") fileName = value.split("/").pop();

  const isImage = (fileName) =>
    [".jpg", ".jpeg", ".png"].some((ext) => fileName?.toLowerCase().endsWith(ext));
  const isPdf = (fileName) => fileName?.toLowerCase().endsWith(".pdf");

  return (
    <div className={styles.uploadBox}>
      <div className={styles.uploadField}>
        {!fileName && icon}

        <input
          ref={fileInputRef}
          type="file"
          name={name}
          onChange={handleChange}
          className={styles.fileInput}
          accept={acceptedTypes.join(",")}
        />

        {!fileName && (
          <button
            type="button"
            className={styles.uploadLabel}
            onClick={handleClick}
          >
            Upload {label}
          </button>
        )}

        {fileName && (
          <div className={styles.previewWrapper}>
            {isImage(fileName) && (
              <div className={styles.previewImageWrapper}>
                <Image
                  src={value}
                  alt="Preview"
                  fill
                  style={{ objectFit: "contain" }}
                  className={styles.previewImage}
                />
              </div>
            )}

            {isPdf(fileName) && (
              <div className={styles.pdfPreviewBox}>
                <p className={styles.pdfText}>
                  PDF uploaded: <strong>{fileName}</strong>
                </p>
                <div className={styles.pdfButtons}>
                  <button
                    type="button"
                    onClick={handleView}
                    className={styles.viewButton}
                  >
                    View
                  </button>
                  <button
                    type="button"
                    className={styles.removeButton}
                    onClick={handleRemove}
                  >
                    <FaTimes />
                  </button>
                </div>
              </div>
            )}

            {!isPdf(fileName) && (
              <button
                type="button"
                className={styles.removeButton}
                onClick={handleRemove}
              >
                <FaTimes />
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

FileUpload.propTypes = {
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  type: PropTypes.oneOf(["receipt", "photo", "general", "document"]),
  value: PropTypes.string,
  allowedTypes: PropTypes.arrayOf(PropTypes.string),
};

export { FileUpload };
