import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";

const DigitalLibraryContainer = () => {
    return(
        <div className={classes.digital_library_container}>
            <div className={classes.digital_library_content}>
                <p className={classes.digital_library_header}>Digital library</p>


                <div className={classes.books_container}>
                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}><PERSON><PERSON><PERSON></p>
                        <Link target="_blank" href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/ebook/ramcharitmanas.pdf"} className={classes.read_btn}>Read Book</Link>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Srimad Bhagavad Gita</p>
                        <Link target="_blank" href={"https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/pdf/ebook/279.pdf"} className={classes.read_btn}>Read Book</Link>
                    </div>
                </div>
                <br />
                <br />
                <br />

                <p className={classes.digital_library_header}>From class 1st - 10th</p>
                <div className={classes.books_container}>
                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 1</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 2</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 3</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 4</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 5</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 6</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 7</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 8</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 9</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>

                    <div className={classes.books_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/book.webp`} width={536} height={488} alt="book" className={classes.book_icon} />
                        <p className={classes.book_name}>Class 10</p>
                        <button className={classes.read_btn}>Read Book</button>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default DigitalLibraryContainer;