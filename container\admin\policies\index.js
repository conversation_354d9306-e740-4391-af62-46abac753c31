"use client";
import React, { useState } from "react";
import Pagination from "@/components/admin/pagination";
import styles from "./styles.module.scss";
import FileUploadModal from "@/components/admin/fileupload";
import { FiUpload } from "react-icons/fi";
import { toast } from "react-toastify";
import {
  useGetPoliciesQuery,
  useAddPolicyMutation,
  useDeletePolicyMutation,
} from "@/injectEndpoints/adminendpoint";

export default function PoliciesContainer() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Input configurations for policies
  const policyInputConfigs = [
    {
      label: "Policy Year:",
      type: "text",
      name: "advertisementNumber",
      placeholder: "Enter Policy Year",
      required: true,
      style: { textTransform: "uppercase" },
      onInput: (e) => (e.target.value = e.target.value.toUpperCase()),
    },
    {
      label: "Policy Name:",
      type: "text",
      name: "notificationText",
      placeholder: "Enter Policy Name",
      required: true,
    },
  ];

  const [addPolicy] = useAddPolicyMutation();
  const [deletePolicy] = useDeletePolicyMutation();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const openFileupload = () => setIsModalVisible(true);
  const handleCloseModal = () => setIsModalVisible(false);

  const {
    data: policiesData,
    error,
    isLoading: isFetchingPolicies,
    refetch: refetchPolicies,
  } = useGetPoliciesQuery({
    programCode: "LISTPOLICY",
    policyId: "%%",
  });

  const policies = policiesData?.data?.data || [];
  const totalItems = policiesData?.data?.totalCount || 0;

  const handleAddPolicy = async (formData) => {
    try {
      await addPolicy({
        programCode: "ADDPOLICY",
        policyName: formData.notificationText,
        policyYear: formData.advertisementNumber,
        policyFileUrl: formData.notificationFileUrl,
      }).unwrap();

      toast.success("Policy added successfully!", {
        position: "top-center",
      });
    } catch (err) {
      toast.error("Failed to add policy: " + err.message, {
        position: "top-center",
      });
    } finally {
      handleCloseModal();
    }
  };

  const handleRemove = async (id) => {
    try {
      await deletePolicy({
        programCode: "DELETEPOLICY",
        policyId: id,
      }).unwrap();

      toast.success("Policy deleted successfully!", {
        position: "top-center",
      });
      const result = await refetchPolicies();
      const updatedPolicies = result?.data?.data?.data || [];
      const updatedTotalItems = updatedPolicies.length;
      const maxPage = Math.ceil(updatedTotalItems / itemsPerPage);
      // If current page is now out of bounds, reset to page 1
      if (currentPage > maxPage) {
        setCurrentPage(1);
      }
    } catch (err) {
      toast.error("Failed to delete policy: " + err.message, {
        position: "top-center",
      });
    }
  };

  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentPolicies = policies.slice(startIndex, startIndex + itemsPerPage);

  if (isFetchingPolicies) {
    return <div>Loading policies...</div>;
  }

  if (error) {
    return <div>Error loading policies: {error.message}</div>;
  }

  return (
    <div className={styles.notifications}>
      <div className={styles.header}>
        <h3 className={styles.title}>View/Upload Policies:</h3>
        <button className={styles.addBtn} onClick={openFileupload}>
          <FiUpload /> &nbsp; Upload New
        </button>
      </div>

      <div className={styles.list}>
        {currentPolicies.length > 0 ? (
          currentPolicies.map((policy) => (
            <div
              key={policy.policyId}
              className={styles.item}
              id={`policy-${policy.policyId}`}
              data-id={policy.policyId}
            >
              <p>{policy.policyName}</p>
              <div className={styles.actions}>
                <a
                  href={policy.policyFileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.edit}
                >
                  View
                </a>

                <button
                  className={styles.remove}
                  onClick={() => handleRemove(policy.policyId)}
                >
                  Remove
                </button>
              </div>
            </div>
          ))
        ) : (
          <p className={styles.empty}>No policies found.</p>
        )}
      </div>

      <FileUploadModal
        advertisementText="You can upload any file type."
        onClose={handleCloseModal}
        onSubmit={handleAddPolicy}
        isVisible={isModalVisible}
        inputConfigs={policyInputConfigs}
      />

      <Pagination
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
      />
    </div>
  );
}
