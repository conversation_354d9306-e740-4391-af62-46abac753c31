@import "../../../assets/css/global.scss";

.notification{
    width: 100%;
    height: auto;
    padding: 80px 50px;

    .notification_content{
        width: 100%;
        height: auto;

        .notification_box{
            width: 100%;
            height: auto;
            margin: 0 auto;

            .notification_header{
                width: 100%;
                height: auto;
                padding: 15px;
                background-color: #571F0B;
                border-radius: 23px 23px 0px 0px;

                .header_text{
                    color: white;
                    font-size: 28px;
                    font-family: $poppinsBold700;
                    text-align: center;
                }
            }

            .notification_body{
                width: 100%;
                min-height: 300px;
                border: 4px solid #571F0B33;
                border-top: unset;
                border-radius: 0px 0px 24px 24px;
                padding: 25px 60px;

                .notification_list{
                    li{
                        color: #571F0B;
                        font-family: $poppinsMedium500;
                        font-size: 16px;
                        margin-bottom: 20px;

                        .date{
                            display: flex;
                            align-items: center;
                            margin-top: 10px;

                            .date_icon{
                                width: 22px;
                                height: auto;
                                margin-right: 5px;
                            }
                        }

                        &:last-child{
                            margin-bottom: unset;
                        }
                    }
                }
            }
        }
    }
}