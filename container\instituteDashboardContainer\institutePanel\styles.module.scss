.container {
  font-family: "Poppins", sans-serif;
  padding: 20px;
  color: #571f0b;
  margin: 0 auto;
  max-width: 1200px;
  margin-top: 70px;
  margin-bottom: 50px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    font-size: 34px;
    font-weight: 700;
    line-height: 100%;
    letter-spacing: 0%;
    margin: 0;
    color: #571f0b;
  }

  p {
    font-size: 0.9rem;
    color: #571f0b;
  }
}

.headerBtns {
  display: flex;
  gap: 12px;
}

/* Outline Button Style */
.outlineBtn {
  padding: 8px 16px;
  border: 2px solid #571f0b;
  border-radius: 6px;
  background: transparent;
  color: #571f0b;
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    background: #4b1509; /* Darker color */
    color: #000; /* Black text on hover */
  }
}

/* Filled Button Style */
.filledBtn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: #571f0b;
  color: white;
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    opacity: 0.8;
    background: #4b1509; /* Darker color */
  }
}

/* School Info Section */
.schoolInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 25px 0;
}

/* School Title Styling */
.schoolTitle {
  display: flex;
  align-items: center;
  gap: 12px;

  h4 {
    margin: 0;
    font-family: "Poppins", sans-serif;
    font-weight: 600; /* SemiBold */
    font-size: 24px;
    line-height: 100%;
    letter-spacing: 0%; /* Ensures the letter-spacing is normal */
  }

  p {
    margin-top: 6px;
    font-family: "Poppins", sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
  }
}

/* Add New Button */
.addNew {
  background: #571f0b;
  color: #fff;
  padding: 6px 14px;
  border-radius: 6px;
  border: none;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

/* Info Card Styling */
.infoCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: transparent;
  padding: 22px 26px;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
  border: 2px solid #571f0b;

  span {
    flex: 1;
    margin-left: 10px;
    font-size: 0.95rem;
  }

  .actions {
    display: flex;
    gap: 8px;
  }
}

.photoSection {
  margin-top: 40px;

  .photoHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      font-size: 24px;
      font-weight: 600;
      color: #571f0b;
    }
  }

  .carouselWrapper {
    display: flex;
    align-items: center;
    margin-top: 20px;
    gap: 10px;
  }

  .navBtn {
    background: #571f0b;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    font-size: 18px;
    cursor: pointer;
    transition: 0.3s;

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }
  }

  .photos {
    display: grid;
    grid-template-columns: repeat(2, 1fr); 
    gap: 20px;
    flex: 1;
  }

  .photoItem {
    position: relative;
    width: 100%;
    max-width: 600px;
    height: 300px; 
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  }

  .schoolImage {
    object-fit: contain;
  }

  .imageBtn {
    border: none;
    background: transparent;
    padding: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .uploadList {
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .uploadRow {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #571f0b;
    border-radius: 6px;
    background: #fafafa;
  }

  .urlText {
    flex: 1;
    font-size: 0.9rem;
    color: #571f0b;
    word-break: break-all;
  }

  .removeBtn {
    background: #e74c3c;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
  }

  .filledBtn {
    margin-top: 20px;
    padding: 10px 18px;
    border: none;
    border-radius: 6px;
    background: #571f0b;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    transition: 0.3s;

    &:hover {
      transform: translateY(-2px);
      opacity: 0.9;
    }
  }
}

.formSection {
  display: flex;
  margin-top: 30px;
  padding: 20px;
  border: 2px solid #571f0b;
  border-radius: 10px;
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.08);
  gap: 20px;

  h4 {
    margin-bottom: 12px;
    color: #571f0b;
  }

  select,
  input {
    width: 100%;
    padding: 10px;
    margin-bottom: 12px;
    border-radius: 6px;
    border: 1px solid #571f0b;
    font-size: 1rem;
  }
}

.photoItem {
  position: relative;
  width: 150px;
  height: 100px;
  margin: 5px;
}

.removeBtn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: red;
  color: white;
  border: none;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  cursor: pointer;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.uploadControls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editBtnEnhanced {
  background: transparent;
  border: none;
  cursor: pointer;
  color: #571f0b;
  &:hover {
    color: #571f0b;
    transform: scale(1.2);
  }
}

