$robotoThin100: RobotoThin100;
$robotoExtraLight200: RobotoExtraLight200;
$robotoLight300: RobotoLight300;
$robotoRegular400: RobotoRegular400;
$robotoMedium500: RobotoMedium500;
$robotoSemiBold600: RobotoSemiBold600;
$robotoBold700: RobotoBold700;
$robotoExtraBold800: RobotoExtraBold800;
$robotoBlack: RobotoBlack;

@font-face {
    font-family: RobotoThin100;
    src: url('../fonts/roboto/RobotoCondensed-Thin.ttf');
}
@font-face {
    font-family: RobotoExtraLight200;
    src: url('../fonts/roboto/RobotoCondensed-ExtraLight.ttf');
}
@font-face {
    font-family: RobotoLight300;
    src: url('../fonts/roboto/RobotoCondensed-Light.ttf');
}
@font-face {
    font-family: RobotoRegular400;
    src: url('../fonts/roboto/RobotoCondensed-Regular.ttf');
}
@font-face {
    font-family: RobotoMedium500;
    src: url('../fonts/roboto/RobotoCondensed-Medium.ttf');
}
@font-face {
    font-family: RobotoSemiBold600;
    src: url('../fonts/roboto/RobotoCondensed-SemiBold.ttf');
}
@font-face {
    font-family: RobotoBold700;
    src: url('../fonts/roboto/RobotoCondensed-Bold.ttf');
}
@font-face {
    font-family: RobotoExtraBold800;
    src: url('../fonts/roboto/RobotoCondensed-ExtraBold.ttf');
}
@font-face {
    font-family: RobotoBlack;
    src: url('../fonts/roboto/RobotoCondensed-Black.ttf');
}

$poppinsThin100: PoppinsThin100;
$poppinsExtraLight200: PoppinsExtraLight200;
$poppinsLight300: PoppinsLight300;
$poppinsRegular400: PoppinsRegular400;
$poppinsMedium500: PoppinsMedium500;
$poppinsSemiBold600: PoppinsSemiBold600;
$poppinsBold700: PoppinsBold700;
$poppinsExtraBold800: PoppinsExtraBold800;
$poppinsBlack900: PoppinsBlack900;

@font-face {
    font-family: PoppinsThin100;
    src: url('../fonts/poppins/Poppins-Thin.ttf');
}
@font-face {
    font-family: PoppinsExtraLight200;
    src: url('../fonts/poppins/Poppins-ExtraLight.ttf');
}
@font-face {
    font-family: PoppinsLight300;
    src: url('../fonts/poppins/Poppins-Light.ttf');
}
@font-face {
    font-family: PoppinsRegular400;
    src: url('../fonts/poppins/Poppins-Regular.ttf');
}
@font-face {
    font-family: PoppinsMedium500;
    src: url('../fonts/poppins/Poppins-Medium.ttf');
}
@font-face {
    font-family: PoppinsSemiBold600;
    src: url('../fonts/poppins/Poppins-SemiBold.ttf');
}
@font-face {
    font-family: PoppinsBold700;
    src: url('../fonts//poppins/Poppins-Bold.ttf');
}

@font-face {
    font-family: PoppinsExtraBold800;
    src: url('../fonts/poppins/Poppins-ExtraBold.ttf');
}
@font-face {
    font-family: PoppinsBlack900;
    src: url('../fonts/poppins/Poppins-Black.ttf');
}

$rockWellExtraBold: RockWellExtraBold;
@font-face {
    font-family: RockWellExtraBold;
    src: url('../fonts/rockwell/Rockwell-Bold.ttf');
}

$interThin100: InterThin100; 
$interExtraLight200: InterExtraLight200;
$interLight300: InterLight300;
$interRegular400: InterRegular400; 
$interMedium500: InterMedium500;
$interSemiBold600: InterSemiBold600;
$interBold700: InterBold700;
$interExtraBold800: InterExtraBold800; 
$interBlack900: InterBlack900;

@font-face {
    font-family: InterThin100;
    src: url('../fonts/inter/Inter_18pt-Thin.ttf');
}
@font-face {
    font-family: InterExtraLight200;
    src: url('../fonts/inter/Inter_18pt-ExtraLight.ttf');
}
@font-face {
    font-family: InterLight300;
    src: url('../fonts/inter/Inter_18pt-Light.ttf');
}
@font-face {
    font-family: InterRegular400;
    src: url('../fonts/inter/Inter_18pt-Regular.ttf');
}
@font-face {
    font-family: InterMedium500;
    src: url('../fonts/inter/Inter_18pt-Medium.ttf');
}
@font-face {
    font-family: InterSemiBold600;
    src: url('../fonts/inter/Inter_18pt-SemiBold.ttf');
}
@font-face {
    font-family: InterBold700;
    src: url('../fonts/inter/Inter_18pt-Bold.ttf');
}
@font-face {
    font-family: InterExtraBold800;
    src: url('../fonts/inter/Inter_18pt-ExtraBold.ttf');
}
@font-face {
    font-family: InterBlack900;
    src: url('../fonts/inter/Inter_18pt-Black.ttf');
}

$rakkasRegular: RakkasRegular;

@font-face {
    font-family: RakkasRegular;
    src: url('../fonts/rakkas/Rakkas-Regular.ttf');
}
 

$darkBrown: #571F0B;
$reddishBrown: #833C23;
$burntCoffee: #3B1406;
$white: #ffffff;
$black: #000000;
$slowWhite: #F9F4F0;
$borderOne: #571F0B33;
$borderTwo: #FFFFFF54;
$textBlack: #000000CC;
$textBlackTwo: #0F172A;