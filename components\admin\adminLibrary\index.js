import Image from "next/image";
import Link from "next/link";
import classes from "./styles.module.scss";

export default function AdminLibrary({ sections, cdnBase, onOpenBook }) {
  const fallbackCover = `${cdnBase}images/book.webp`;

  return (
    <div className={classes.digital_library_container}>
      <div className={classes.digital_library_content}>
        {sections.map((section, index) => (
          <div key={section.title}>
            <p className={classes.digital_library_header}>{section.title}</p>
            <div className={classes.books_container}>
              {section.books.map((book) => (
                <div key={book.id} className={classes.books_card}>
                  <Image
                    src={book.coverUrl || fallbackCover}
                    width={120}
                    height={200}
                    alt={book.title || "Book cover"}
                    className={classes.book_icon}
                  />
                  <p className={classes.book_name}>{book.title}</p>

                  {book.readUrl ? (
                    <Link
                      target="_blank"
                      href={book.readUrl}
                      className={classes.read_btn}
                    >
                      {book.ctaLabel || "Read Book"}
                    </Link>
                  ) : (
                    <button
                      className={classes.read_btn}
                      onClick={() => onOpenBook?.(book)}
                    >
                      {book.ctaLabel || "Read Book"}
                    </button>
                  )}
                </div>
              ))}
            </div>
            {index < sections.length - 1 && (
              <>
                <br />
                <br />
                <br />
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Add PropTypes validation
import PropTypes from "prop-types";

AdminLibrary.propTypes = {
  sections: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
      books: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
            .isRequired,
          coverUrl: PropTypes.string,
          title: PropTypes.string.isRequired,
          readUrl: PropTypes.string,
          ctaLabel: PropTypes.string,
        })
      ).isRequired,
    })
  ).isRequired,
  cdnBase: PropTypes.string.isRequired,
  onOpenBook: PropTypes.func,
};
