import React, { useState } from "react";
import Table from "@/components/table";
import classes from "./styles.module.scss";
import { sanskritSchools } from "./schoolnames";
import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import TextField from "@mui/material/TextField";

const InstitutionsContainer = () => {
    const [currentPage, setCurrentPage] = useState(1);
    const [districtSearch, setDistrictSearch] = useState("");
    const [codeSearch, setCodeSearch] = useState("");
    const ROWS_PER_PAGE = 8;

    if (!sanskritSchools || sanskritSchools.length === 0) {
        return <div>No data available.</div>;
    }

    const headerRow = sanskritSchools[0];

    const keys = Object.keys(headerRow).filter((_, index) => index !== 0);

    const columns = [
        { name: "serialNo", label: "S.No." },
        ...keys.map((key) => ({
            name: key,
            label: headerRow[key].toString().trim(),
        })),
    ];

    const allRows = sanskritSchools.slice(1).map((row, index) => ({
        serialNo: index + 1,
        ...row,
    }));

    const filteredRows = allRows.filter((row) => {
        const district = row["__EMPTY"]?.toLowerCase() || "";
        const code = row["__EMPTY_4"]?.toString() || "";

        return (
            district.includes(districtSearch.toLowerCase()) &&
            code.includes(codeSearch)
        );
    });

    const pageCount = Math.ceil(filteredRows.length / ROWS_PER_PAGE);
    const paginatedRows = filteredRows.slice(
        (currentPage - 1) * ROWS_PER_PAGE,
        currentPage * ROWS_PER_PAGE
    );

    const handlePageChange = (event, page) => {
        setCurrentPage(page);
    };

    return (
        <div className={classes.institutions_container}>
            <div className={classes.institutions_content}>
                <p className={classes.institutions_header}>Institutions</p>

                    <div className={classes.search_bar}>
                        <TextField
                            label="Search by District"
                            variant="outlined"
                            size="small"
                            value={districtSearch}
                            onChange={(e) => {
                                setDistrictSearch(e.target.value);
                                setCurrentPage(1);
                            }}
                        />

                        <TextField
                            label="Search by Code"
                            variant="outlined"
                            size="small"
                            value={codeSearch}
                            onChange={(e) => {
                                setCodeSearch(e.target.value);
                                setCurrentPage(1);
                            }}
                        />
                    </div>


                <Table columns={columns} rows={paginatedRows} variant="table_one" />

                <div className={classes.pagination}>
                    <Stack spacing={2}>
                        <Pagination
                            count={pageCount}
                            page={currentPage}
                            onChange={handlePageChange}
                            color="primary"
                            sx={{
                                "& .MuiPaginationItem-root.MuiPaginationItem-page": {
                                    borderRadius: "6px",
                                    margin: "0 10px",
                                    color: "#571F0B",
                                    "&:hover": {
                                        backgroundColor: "#6e2a12",
                                        color: "#fff"
                                    },
                                    "&.Mui-selected": {
                                        color: "#fff",
                                        backgroundColor: "#571F0B",
                                    },
                                },
                            }}
                        />
                    </Stack>
                </div>
            </div>
        </div>
    );
};

export default InstitutionsContainer;
