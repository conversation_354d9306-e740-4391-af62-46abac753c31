"use client";
import React, { useState } from "react";
import PropTypes from "prop-types";
import {
  FaEdit,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaUniversity,
  FaSchool,
  FaHashtag,
} from "react-icons/fa";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import styles from "../styles.module.scss";

const InstituteContacts = ({
  contactsData,
  addContact,
  updateContact,
  refetchContacts,
  nodalSchools = [],
}) => {
  const { instituteDetails } = useSelector((state) => state.auth);
  const showNodalSchool =
    instituteDetails?.typeOfSchool === "प्रस्तावित";

  const [formState, setFormState] = useState({
    contactType: "",
    contactValue: "",
    contactId: "",
    open: false,
  });

  const contactTypeLabels = {
    institute_name_eng: "School Name",
    institute_code: "Institute Code",
    phone: "Phone No",
    email: "Email ID",
    map_location: "Address",
    nodal_school: "Nodal School",
  };

  const contacts = contactsData || [];

  const getSingleContact = (type) =>
    contacts.find((c) => c?.contactType === type) || null;

  const getAllContacts = (type) =>
    contacts.filter((c) => c?.contactType === type);

  const validateInput = (type, value) => {
    if (type === "institute_code") {
      if (!/^\d*$/.test(value)) {
        toast.error("Institute Code must contain only numbers!", {
          position: "top-center",
        });
        return false;
      }
    }
    if (type === "institute_name_eng" || type === "nodal_school") {
      if (!/^[A-Z\s]*$/.test(value)) {
        toast.error("Only uppercase English letters allowed!", {
          position: "top-center",
        });
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async () => {
    try {
      if (!formState.contactValue) {
        toast.error("Please select or enter a value!", {
          position: "top-center",
        });
        return;
      }

      const valid = validateInput(
        formState.contactType,
        formState.contactValue
      );
      if (!valid) return;

      const payload = {
        contactType: formState.contactType,
        contactValue: formState.contactValue,
      };

      if (formState.contactId) {
        await updateContact({
          contactId: formState.contactId,
          ...payload,
        }).unwrap();
      } else {
        await addContact(payload).unwrap();
      }

      setFormState({
        contactType: "",
        contactValue: "",
        contactId: "",
        open: false,
      });

      refetchContacts();
      toast.success("Contact saved successfully!", {
        position: "top-center",
      });
    } catch (err) {
      console.error("Error submitting contact:", err);
      toast.error("Contact submission failed!", { position: "top-center" });
    }
  };

  const handleEdit = (type) => {
    const existingContact = getSingleContact(type);
    setFormState({
      contactType: type,
      contactValue:
        existingContact?.contactVype || existingContact?.contactValue || "",
      contactId:
        existingContact?.id || existingContact?.contactId || "",
      open: true,
    });
  };

  const renderEditableCard = (label, type, icon, multiple = false) => {
    const contactList = multiple
      ? getAllContacts(type)
      : [getSingleContact(type)].filter(Boolean);

    const values =
      contactList.length > 0
        ? contactList.map((c) => c.contactVype || c.contactValue).join(", ")
        : "N/A";

    return (
      <div className={styles.infoCard}>
        <div className={styles.iconLabel}>
          {icon}
          <span>
            {label}: {values}
          </span>
        </div>
        <div className={styles.actions}>
          <button
            onClick={() => handleEdit(type)}
            className={styles.editBtnEnhanced}
            title="Edit"
          >
            <FaEdit size={22} />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.infoCardsWrapper}>
      <div className={styles.inlineWrapper}>
        {renderEditableCard(
          "School Name",
          "institute_name_eng",
          <FaUniversity size={20} />
        )}
        {renderEditableCard(
          "Institute Code",
          "institute_code",
          <FaHashtag size={20} />
        )}
      </div>

      {renderEditableCard(
        "Phone No",
        "phone",
        <FaPhone style={{ transform: "rotate(90deg)" }} size={20} />,
        true
      )}
      {renderEditableCard("Email ID", "email", <FaEnvelope size={20} />)}
      {renderEditableCard(
        "Address",
        "map_location",
        <FaMapMarkerAlt size={20} />
      )}

      {showNodalSchool &&
        renderEditableCard("Nodal School", "nodal_school", <FaSchool size={20} />)}

      {formState.open && (
        <div className={styles.formSection}>
          <h4>
            {formState.contactId
              ? `Update ${contactTypeLabels[formState.contactType]}`
              : `Add ${contactTypeLabels[formState.contactType]}`}
          </h4>

          {formState.contactType === "nodal_school" ? (
            <select
              value={formState.contactValue}
              onChange={(e) =>
                setFormState({ ...formState, contactValue: e.target.value })
              }
            >
              <option value="">Select Nodal School</option>
              {nodalSchools.map((school) => (
                <option key={school.id} value={school.schoolName}>
                  {school.schoolName}
                </option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              placeholder={`Enter ${contactTypeLabels[formState.contactType]}`}
              value={formState.contactValue}
              onChange={(e) => {
                let newValue = e.target.value;
                if (formState.contactType === "institute_code") {
                  newValue = newValue.replace(/\D/g, "");
                } else if (
                  formState.contactType === "institute_name_eng" ||
                  formState.contactType === "nodal_school"
                ) {
                  newValue = newValue.toUpperCase().replace(/[^A-Z\s]/g, "");
                }
                setFormState({
                  ...formState,
                  contactValue: newValue,
                });
              }}
            />
          )}

          <button className={styles.filledBtn} onClick={handleSubmit}>
            {formState.contactId ? "Update Contact" : "Add Contact"}
          </button>
        </div>
      )}
    </div>
  );
};

InstituteContacts.propTypes = {
  contactsData: PropTypes.array,
  addContact: PropTypes.func.isRequired,
  updateContact: PropTypes.func.isRequired,
  refetchContacts: PropTypes.func.isRequired,
  nodalSchools: PropTypes.array,
};

export default InstituteContacts;
