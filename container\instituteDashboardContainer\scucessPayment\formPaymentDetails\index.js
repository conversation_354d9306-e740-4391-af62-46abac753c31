"use client";
import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useGetPaymentStatusMutation } from "@/injectEndpoints/paymentendpoint";
import { useGetStudentRegistrationListQuery } from "@/injectEndpoints/studentRegistrationendpoint";
import RegistrationPreview from "@/components/view-registration-details";

const PaymentStatusChecker = ({ txnid, showPreview, onClosePreview, hidePreviewButton }) => {
  const [getPaymentStatus, { data, isLoading, error }] =
    useGetPaymentStatusMutation();
  const [registration, setRegistration] = useState(null);

  useEffect(() => {
    if (txnid) {
      getPaymentStatus({ programCode: "PAYMENTSTATUS", txnid });
    }
  }, [txnid, getPaymentStatus]);

  const registrationId = data?.data?.registrationId;

  const { data: registrationData } = useGetStudentRegistrationListQuery(
    {
      instituteId: "",
      registrationId: registrationId || "",
      pageNo: 1,
      itemsPerPage: 1,
      search: "",
    },
    { skip: !registrationId }
  );

  useEffect(() => {
    if (registrationData?.data?.length > 0) {
      setRegistration(registrationData.data[0]);
    }
  }, [registrationData]);

  if (isLoading) return <p>Loading payment details...</p>;
  if (error) return <p style={{ color: "red" }}>Error fetching payment details.</p>;

  const payment = data?.data;

  if (showPreview && registration) {
    return (
      <div
        style={{
          maxWidth: "900px",
          width: "90%",
          margin: "20px auto",
          padding: "20px",
          backgroundColor: "#fff",
          boxShadow: "0 4px 15px rgba(0,0,0,0.2)",
          borderRadius: "8px",
        }}
      >
        <div style={{ display: "flex", justifyContent: "center", gap: "16px", marginBottom: "16px" }}>
          <button
            style={{
              padding: "8px 16px",
              border: "none",
              background: "#5c2d0c",
              color: "#fff",
              cursor: "pointer",
              borderRadius: "6px",
            }}
            onClick={onClosePreview}
          >
            ← Back
          </button>
          <button
            style={{
              padding: "8px 16px",
              border: "none",
              background: "#5c2d0c",
              color: "#fff",
              cursor: "pointer",
              borderRadius: "6px",
            }}
            onClick={() => {
              const printContents = document.getElementById("registration-preview").innerHTML;
              const originalContents = document.body.innerHTML;
              document.body.innerHTML = printContents;
              window.print();
              document.body.innerHTML = originalContents;
              window.location.reload();
            }}
          >
            🖨️ Print
          </button>
        </div>

        <div id="registration-preview">
          <RegistrationPreview registration={registration} />
        </div>
      </div>
    );
  }

  return (
    <div style={{ marginTop: "16px", textAlign: "left" }}>
      {data?.status === "success" && payment ? (
        <div>
          <h3>
            Payment Status: {payment.status === "success" ? "Successful ✅" : "Failed ❌"}
          </h3>
          <p>
            <strong>Transaction ID:</strong> {payment.txnid}
          </p>
          <p>
            <strong>Registration ID:</strong> {payment.registrationId}
          </p>
          <p>
            <strong>Form No:</strong> {payment.formNo}
          </p>
          <p>
            <strong>Amount:</strong> ₹{payment.amount}
          </p>

          {!hidePreviewButton && registration && (
            <button
              style={{ marginTop: "12px" }}
              onClick={() => onClosePreview(true)}
            >
              👁 Preview Registration
            </button>
          )}
        </div>
      ) : (
        <p>Payment not found or failed ❌</p>
      )}
    </div>
  );
};

PaymentStatusChecker.propTypes = {
  txnid: PropTypes.string.isRequired,
  showPreview: PropTypes.bool,
  onClosePreview: PropTypes.func,
  hidePreviewButton: PropTypes.bool,
};

export default PaymentStatusChecker;
