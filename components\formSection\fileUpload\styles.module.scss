.uploadBox {
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  padding: 10px;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.uploadField {
  width: 220px;
  height: 190px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px solid var(--border-color, #571f0b);
  border-radius: 6px;
  cursor: pointer;
  color: var(--primary-color, #571f0b);
  font-size: 0.9rem;
  position: relative;
  padding: 10px;
  text-align: center;
  box-sizing: border-box;
  transition: background 0.3s ease;

  input[type="file"] {
    opacity: 0;
    position: absolute;
    inset: 0;
    cursor: pointer;
  }

  &:hover {
    background: rgba(87, 31, 11, 0.05);
  }

  .uploadIcon {
    font-size: 70px;
    margin-bottom: 8px;
  }
}

.uploadLabel {
  font-size: 16px;
  font-weight: 400;
  padding: 10px;
  background-color: var(--button-bg-color, #571f0b);
  color: var(--button-text-color, #fff);
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border: 1px solid var(--button-border-color, #4a1a0a);
  margin-top: 10px;

  &:hover {
    background-color: #4a1a0a;
  }
}

/* Preview wrapper */
.previewWrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Image Preview */
.previewImageWrapper {
  margin-top: 8px;
  width: 100%;
  max-width: 200px;
  height: 150px;
  position: relative;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.previewImage {
  object-fit: contain;
}

/* PDF Preview Box */
.pdfPreviewBox {
  margin-top: 12px;
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  background: #fafafa;
  text-align: center;
}

.pdfText {
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 8px;
  word-break: break-word;
}

.pdfButtons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.viewButton {
  background-color: #4a1a0a;
  color: #fff;
  padding: 4px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;

  &:hover {
    background-color: #fff;
    color: #4a1a0a;

  }
}
.removeButton {
  background-color: #4a1a0a;
  color: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background: #fff;
    color: #571f0b;
  }
}
