$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;

.container {
  font-family: "Poppins", sans-serif;
  padding: 20px;
  margin-top: 70px;
  color: #571f0b;
}

.pageTitle {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 20px;
}

.noData {
  font-size: 1rem;
  color: gray;
  text-align: center;
  margin-top: 30px;
}

/* Table Styling */
.table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);

  th,
  td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
  }

  th {
    background: #571f0b;
    color: #fff;
    font-weight: 600;
  }


}

/* Status Styling */
.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 600;
}

.status.Pending {
  background: #ffe6cc;
  color: #b36b00;
}

.status.Approved {
  background: #d6f5d6;
  color: #2d7a2d;
}

.status.Rejected {
  background: #f8d7da;
  color: #a94442;
}

/* Action Buttons */
.actions {
  display: flex;
  gap: 8px;

  button {
    border: none;
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: 0.3s;

    &:hover {
      opacity: 0.8;
    }
  }
}

.deleteBtn {
  background: #ff4d4d;
  color: white;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 8px;
}

.pageBtn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pageBtn:hover:not(:disabled) {
  background: #0070f3;
  color: #fff;
}

.pageBtn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.activePage {
  background: #0070f3;
  color: white;
  font-weight: bold;
  border-color: #0070f3;
}

.buttonRow {
  display: flex;
  gap: 16px;
  margin-bottom: 50px;
  margin-top: 30px;
}

.halfButton {
  flex: 1;
}

.halfButton button {
  width: 100%;
  padding: 12px 30px;
  background-color: $primary;
  color: white;
  border-radius: 6px;
  font-size: 16px;
  border: 2px solid $primary;
  cursor: pointer;

  &:hover {
    background-color: transparent;
    color: $primary;
  }
}

.searchBar {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;

  input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid $border;
    border-radius: 6px;
    font-size: 14px;
    background: transparent;
    color: $primary;

    &:focus {
      outline: none;
      border-color: $primary;
      box-shadow: 0 0 0 2px rgba(87, 31, 11, 0.2);
    }
  }
}


.repayButton {
  background-color: #e6b800;
  color: #fff;
  padding: 4px 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-left: 6px;
  font-weight: 500;
  transition: 0.2s ease-in-out;

  &:hover {
    background-color: #d19f00;
  }
}




/* Hide top section and current date/time only during printing */
@media print {

  .topSection,
  .currentDateTime {
    display: none !important;
  }
}