$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;

.registrationContainer {
  padding: 50px;
  border-radius: 8px;
  font-family: $font-family;
  color: $primary;
  width: 100%;
  max-width: 1388px;
  margin: 20px auto;
  box-sizing: border-box;
}

.instructionButton {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50px;
  width: 100%;

  button {
    margin-top: 20px;
    border: 2px solid $primary;
    color: white;
    font-size: 16px;
    padding: 12px 30px;
    border-radius: 6px;
    cursor: pointer;
    background-color: $primary;
    width: 100%;

    &:hover {
      background-color: transparent;
      color: $primary;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;

  h3 {
    font-family: $font-family;
    font-weight: 600;
    font-size: 20px;
    color: $primary;
    text-decoration: underline;
    margin: 0;
  }

  .formNo {
    display: flex;
    border: 1px solid $border;
    padding: 6px;
    border-radius: 4px;

    label {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      margin-right: 10px;
      white-space: nowrap;
    }

    input {
      color: $primary;
    }
  }
}

.mainContent {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.formSections {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.section {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30px;
  padding: 12px 16px;
  border: 1px solid $primary;
  border-radius: 6px;
  background-color: transparent;
  margin-bottom: 20px;
  width: auto;
  min-width: 200px;
}


.section label {
  font-size: 16px;
  font-weight: 500;
  color: $primary;
  white-space: nowrap;
  flex-shrink: 0;
  padding: 10px 10px;
}

.input {
  flex-grow: 1;
  min-width: 0;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px 10px;
}


.sectioncategory {
  margin-bottom: 10px;
}

.sectionnationality {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  width: 100%;

}

.twoparts {
  display: flex;
  gap: 20px;
  width: 100%;

  .section {
    flex: 1;
  }
}

.formBox {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px 10px;
}

.instructionModal {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.addressGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.inputContainer {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  padding: 12px 16px;
  flex-wrap: nowrap;
  width: 100%;
}

.inputContainer Label {
  margin-bottom: 8px;
}

.inputContainer Input {
  padding: 8px;
}

.twoColumnLayout {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  flex-direction: row;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid $primary;
  border-radius: 6px;
  background-color: transparent;
  margin-bottom: 16px;
  width: 100%;
}

.fullWidth {
  width: 100%;
}

.uploadColumn {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.uploadRow {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.fullWidthSelect .selectInput {
  width: 100%;
  padding-right: 30px;
}

.fullWidthSelect {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.twoColumnLayout>div {
  width: 100%;
}


@media (max-width: 1080px) {
  .registrationContainer {
    padding: 40px;
  }

  .mainContent {
    flex-direction: column;
    gap: 20px;
  }

  .formSections {
    width: 100%;
  }

  .twoColumnLayout {
    flex-direction: column;
    gap: 15px;
  }

  .addressGrid {
    grid-template-columns: 1fr 1fr;
  }

  .instructionButton button {
    font-size: 14px;
    padding: 10px 20px;
  }

  .uploadColumn {
    gap: 15px;
  }
}

@media (max-width: 800px) {
  .registrationContainer {
    padding: 30px;
  }

  .mainContent {
    flex-direction: column;
    gap: 15px;
  }

  .formSections {
    width: 100%;
  }

  .section {
    flex-direction: column;
    align-items: flex-start;
  }

  .addressGrid {
    grid-template-columns: 1fr;
  }

  .instructionButton button {
    font-size: 12px;
    padding: 8px 15px;
  }

  .uploadColumn {
    gap: 10px;
  }

  .twoColumnLayout {
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 500px) {
  .registrationContainer {
    padding: 20px;
  }

  .instructionButton button {
    font-size: 12px;
    padding: 8px 10px;
  }

  .mainContent {
    gap: 10px;
  }

  .formSections {
    width: 100%;
  }

  .twoColumnLayout {
    flex-direction: column;
    gap: 8px;
  }

  .addressGrid {
    grid-template-columns: 1fr;
  }

  .uploadColumn {
    gap: 8px;
  }
}

.buttonRow {
  display: flex;
  gap: 16px;
  margin-bottom: 50px;
  margin-top: 30px;
}

.halfButton {
  flex: 1;
}

.halfButton button {
  width: 100%;
  padding: 12px 30px;
  background-color: $primary;
  color: white;
  border-radius: 6px;
  font-size: 16px;
  border: 2px solid $primary;
  cursor: pointer;

  &:hover {
    background-color: transparent;
    color: $primary;
  }
}





.previewImage {
  width: 250px;
  object-fit: cover;
  border-radius: 6px;
  border: 2px solid $primary;
  margin-top: 10px;
  max-width: 100%;
  height: 180px;
}

.nameRow {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.nameColumn {
  flex: 1;
}

.previewImageBlock {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.previewImageColumn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.addressItem {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  border: 1px solid $primary;
  border-radius: 8px;
}

.submissionResultContainer {
  max-width: 600px;
  margin: 50px auto;
  padding: 30px;
  border-radius: 12px;
  background-color: transparent;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  animation: fadeIn 0.5s ease-in-out;
}

.submissionResultContainer h3 {
  font-size: 1.8rem;
  margin-bottom: 15px;
  color: $primary;
}

.submissionResultContainer p {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: $primary;
}

.submissionResultContainer p strong {
  color: #222;
}


.success {
  --status-color: $primary;
  border-left: 6px solid $primary;
}

.failure {
  --status-color: #dc3545;
  border-left: 6px solid #dc3545;
}


@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}



.disabledButton {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.radioGroup {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.radioOption {
  display: flex;
  align-items: center;
  gap: 6px;

}

.previewImageWrapper {
  position: relative;
  width: 220px;
  height: 190px;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f9f9f9;
}


.uploadFallback {
  color: #888;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
}