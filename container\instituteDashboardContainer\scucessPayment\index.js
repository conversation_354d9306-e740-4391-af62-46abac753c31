"use client";
import React, { useState } from "react";
import PropTypes from "prop-types";
import styles from "./styles.module.scss";
import PaymentStatusChecker from "./formPaymentDetails";

const SuccessModal = ({ txnid }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  const handleDashboard = () => {
    window.location.href = "/instituteDashboard";
  };

  return (
    <>
      {/* Main Modal */}
      {!showPreview && (
        <div className={styles.overlay}>
          <div className={styles.modal}>
            {showDetails ? (
              <>
                <PaymentStatusChecker
                  txnid={txnid}
                  showPreview={false}
                  hidePreviewButton={true} 
                  onClosePreview={(preview) =>
                    preview ? setShowPreview(true) : setShowDetails(false)
                  }
                />
                <div className={styles.actions}>
                  <button
                    className={styles.outlineBtn}
                    onClick={() => setShowDetails(false)}
                  >
                    ← Back
                  </button>
                  <button
                    className={styles.fillBtn}
                    onClick={() => setShowPreview(true)}
                  >
                    Preview Registration
                  </button>
                  <button
                    className={styles.outlineBtn}
                    onClick={handleDashboard}
                  >
                    Go to Dashboard
                  </button>
                </div>
              </>
            ) : (
              <>
                <div className={styles.iconWrapper}>
                  <div className={styles.outerCircle}>
                    <div className={styles.checkmark}>✔</div>
                  </div>
                </div>

                <h2>Form Submitted Successfully!</h2>
                <p>We’ve received your details.</p>
                <p>
                  <strong>Transaction ID:</strong> {txnid || "N/A"}
                </p>

                <div className={styles.actions}>
                  <button
                    className={styles.fillBtn}
                    onClick={() => setShowDetails(true)}
                  >
                    View Payment Details
                  </button>
                  <button
                    className={styles.outlineBtn}
                    onClick={handleDashboard}
                  >
                    Go to Dashboard
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Page-width Preview */}
      {showPreview && (
        <PaymentStatusChecker
          txnid={txnid}
          showPreview={true}
          onClosePreview={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

SuccessModal.propTypes = {
  txnid: PropTypes.string,
};

export default SuccessModal;
