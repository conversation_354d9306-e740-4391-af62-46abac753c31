"use client";
import React from "react";
import PropTypes from "prop-types";
import styles from "./styles.module.scss"; // ✅ separate styles file

export default function Pagination({
  totalItems,
  itemsPerPage,
  currentPage,
  setCurrentPage,
}) {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  if (totalPages <= 1) return null;

  const getPages = () => {
    const pages = [];
    const maxVisible = 5;

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      const start = Math.max(2, currentPage - 1);
      const end = Math.min(totalPages - 1, currentPage + 1);

      pages.push(1);
      if (start > 2) pages.push("start-ellipsis");

      for (let i = start; i <= end; i++) pages.push(i);

      if (end < totalPages - 1) pages.push("end-ellipsis");
      pages.push(totalPages);
    }
    return pages;
  };

  const pagesToShow = getPages();

  return (
    <div className={styles.pagination}>
      {/* Previous */}
      <button
        className={styles.pageBtn}
        disabled={currentPage === 1}
        onClick={() => setCurrentPage(currentPage - 1)}
      >
        ‹
      </button>

      {/* Jump Back */}
      {currentPage > 10 && (
        <button
          className={styles.pageBtn}
          onClick={() => setCurrentPage(currentPage - 10)}
        >
          «
        </button>
      )}

      {/* Page Numbers */}
      {pagesToShow.map((page) =>
        page === "start-ellipsis" || page === "end-ellipsis" ? (
          <span key={`ellipsis-${page}`} className={styles.ellipsis}>
            ...
          </span>
        ) : (
          <button
            key={`page-${page}`}
            className={`${styles.pageBtn} ${
              currentPage === page ? styles.active : ""
            }`}
            onClick={() => setCurrentPage(page)}
          >
            {page}
          </button>
        )
      )}

      {/* Jump Forward */}
      {currentPage < totalPages - 10 && (
        <button
          className={styles.pageBtn}
          onClick={() => setCurrentPage(currentPage + 10)}
        >
          »
        </button>
      )}

      {/* Next */}
      <button
        className={styles.pageBtn}
        disabled={currentPage === totalPages}
        onClick={() => setCurrentPage(currentPage + 1)}
      >
        ›
      </button>
    </div>
  );
}

Pagination.propTypes = {
  totalItems: PropTypes.number.isRequired,
  itemsPerPage: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  setCurrentPage: PropTypes.func.isRequired,
};
