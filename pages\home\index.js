"use client";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import HomeContainer from "@/container/homeContainer";

const HomePage = () => {
  const router = useRouter();
  const { token, user } = useSelector((state) => state.auth);

  useEffect(() => {
    if (token && user?.[0]?.userRole === "institute") {
      router.replace("/instituteDashboard");
    }
  }, [token, user, router]);

  return <></>;
};

export default HomePage;
