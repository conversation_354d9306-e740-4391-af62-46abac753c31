@import "../../assets/css/global.scss";

.digital_library_container{
    width: 100%;
    height: auto;
    padding: 50px 120px;

    .digital_library_content{
        width: 100%;
        height: auto;

        .digital_library_header{
            color: #571F0B;
            font-size: 24px;
            font-family: $poppinsSemiBold600;
            border-bottom: 2px solid #571F0B;
            padding-bottom: 8px;
            margin-bottom: 20px;
            display: inline-block;
        }

        .books_container{
            width: 100%;
            height: auto;
            display: flex;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 50px 10px;

            .books_card{
                display: flex;
                align-items: center;
                flex-direction: column;

                .book_icon{
                    width: 220px;
                    height: auto;
                }

                .book_name{
                    color: #571F0B;
                    font-size: 18px;
                    font-family: $poppinsMedium500;
                }

                .read_btn{
                    border: unset;
                    background-color: #571F0B;
                    font-size: 15px;
                    font-family: $poppinsMedium500;
                    color: white;
                    padding: 6px 24px;
                    border-radius: 6px;
                    margin-top: 25px;
                    text-decoration: unset;
                }
            }
        }
    }
}