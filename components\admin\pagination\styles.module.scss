$primary: #571f0b;

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  margin-top: 15px;

  .pageBtn {
    border: 1px solid $primary;
    background: transparent;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    color: $primary;
    transition: all 0.2s ease;

    &.active {
      background: $primary;
      color: #fff;
    }

    &:hover:not(:disabled) {
      background: lighten($primary, 45%);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .ellipsis {
    padding: 0 6px;
    font-size: 14px;
    color: #666;
  }
}
