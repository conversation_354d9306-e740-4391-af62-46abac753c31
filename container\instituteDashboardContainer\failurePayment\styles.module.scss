.crossmark {
  font-size: 3rem;
  color: #5c2d0c;
  border: 3px solid #5c2d0c;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Outer circle */
.outerCircle {
  width: 90px;
  height: 90px;
  border: 5px solid #5c2d0c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}


.overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
}

.modal {
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 500px; // responsive width
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);

  h2 {
    margin: 1rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #5c2d0c;
  }

  p {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 2rem;
    line-height: 1.4;
  }
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.checkmark {
  font-size: 3rem;
  color: #5c2d0c;
  border: 3px solid #5c2d0c;
  border-radius: 50%;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.outlineBtn {
  padding: 0.6rem 1.2rem;
  border: 2px solid #5c2d0c;
  border-radius: 6px;
  background: transparent;
  color: #5c2d0c;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: #fff;
    background: #5c2d0c;
  }
}

.fillBtn {
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 6px;
  background: #5c2d0c;
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: #f6ede6;
    color: #5c2d0c;
    border: 2px solid #5c2d0c;

  }
}
