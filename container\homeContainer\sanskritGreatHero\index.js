import classes from "./styles.module.scss";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { FreeMode, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/free-mode';
import 'swiper/css/pagination';

const SanskritGreatHero = () => {
    return(
        <div className={classes.sanskrit_great_hero}>
            <div className={classes.sanskrit_great_hero_content}>
                <div className={classes.rectangle_heading}>
                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/rectangleOne.webp`} width={2174} height={100} alt="" className={classes.rectangle_one} />
                    <div className={classes.rectangle_text}>
                        <p className={classes.text_}>परीक्षा नियंत्रक - श्री उपेन्द्र कुमार</p>
                        <p className={classes.text_}>प्रधान सहायक - श्री भवनाथ झा</p>
                    </div>
                </div>

                <div className={classes.sanskrit_great_hero_slider_wrapper}>
                    <p className={classes.slider_heading}>संस्कृतस्य महानायकः</p>
                    <div className={classes.slider}>
                        <Swiper
                            slidesPerView={6}
                            spaceBetween={40}
                            freeMode={true}
                            modules={[FreeMode, Autoplay]}
                            loop={true}
                            speed={1500} // Smooth and slow transition
                            autoplay={{
                                delay: 1000, // Continuous scrolling
                                disableOnInteraction: false,
                            }}
                            breakpoints={{
                                1181: {
                                    spaceBetween: 40,
                                },
                                668: {
                                    spaceBetween: 20,
                                },
                                300: {
                                    spaceBetween: 15,
                                    slidesPerView: 4,
                                },
                            }}
                        >
                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Kalidas.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>कालिदास</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Chanakya.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>चाणक्य</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Banabhatta.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>बाणभट्ट</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Panini.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>पाणिनी</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/AdiShankara.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>आदि शंकराचार्य</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Varahamihira.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>वराहमिहिर</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Bhartrihari.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>भरतृहरि</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Bhavabhuti.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>भवभूति</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/VedVyasa.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>वेद व्यास</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Valmiki.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>वाल्मिकी</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Magha.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>माघ</p>
                                </div>
                            </SwiperSlide>

                            <SwiperSlide>
                                <div className={classes.great_hero_card}>
                                    <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Bhasa.webp`} width={412} height={560} alt="hero" />
                                    <p className={classes.hero_name}>भासा</p>
                                </div>
                            </SwiperSlide>
                        </Swiper>
                    </div>
                </div>
            </div>
        </div>
    )
} 

export default SanskritGreatHero;