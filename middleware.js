import { NextResponse } from "next/server";

/**
 * @param {import("next/server").NextRequest} request
 */
export function middleware(request) {
  const { pathname } = request.nextUrl;

  const publicRoutes = ["/", "/about", "/contact", "/login"];
  const instituteRoutes = ["/instituteDashboard"];
  const adminRoutes = ["/admin"];

  const token = request.cookies.get("token")?.value;
  const userRole = request.cookies.get("user_role")?.value;

  const redirectTo = (url) => NextResponse.redirect(new URL(url, request.url));
  const isPublic = () => publicRoutes.includes(pathname);
  const isInstituteRoute = () => instituteRoutes.some((r) => pathname.startsWith(r));
  const isAdminRoute = () => adminRoutes.some((r) => pathname.startsWith(r));

  if ((pathname === "/" || pathname === "/login") && token) {
    if (userRole === "institute") return redirectTo("/instituteDashboard");
    if (userRole === "admin") return redirectTo("/admin");
  }

  if (isPublic()) return NextResponse.next();

  if (isInstituteRoute() && (!token || userRole !== "institute")) {
    return redirectTo("/login");
  }

  if (isAdminRoute() && (!token || userRole !== "admin")) {
    return redirectTo("/login");
  }

}

// Optional matcher to skip static files
// export const config = {
//   matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
// };
