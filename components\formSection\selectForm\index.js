import React from "react";
import PropTypes from "prop-types";
import styles from "./styles.module.scss";
import { Field, ErrorMessage } from "formik";

const SelectForm = ({ type, name, options, placeholder, disabled }) => {
  if (!name || !options || options.length === 0) return null;

  return (
    <>
      {(type === "radio" || type === "checkbox") && (
        <div className={styles.inlineGroup}>
          <div className={styles.inlineOptions}>
            {options.map((option) => (
              <label key={`${name}-${option}`} className={styles.optionItem}>
                <Field type={type} name={name} value={option} disabled={disabled} />
                {option}
              </label>
            ))}
          </div>
          <ErrorMessage name={name} component="div" className={styles.error} />
        </div>
      )}

      {type === "select" && (
        <div className={styles.selectContainer}>
          <Field name={name}>
            {({ field, form }) => (
              <select
                {...field}
                className={styles.selectInput}
                onChange={(e) => form.setFieldValue(name, e.target.value)}

              >
                <option value="">{placeholder || "Select an option"}</option>
                {options.map((option) => (
                  <option key={`${name}-${option}`} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            )}
          </Field>
          <ErrorMessage name={name} component="div" className={styles.error} />
        </div>
      )}
    </>
  );
};

SelectForm.propTypes = {
  type: PropTypes.oneOf(["radio", "checkbox", "select"]).isRequired,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(PropTypes.string).isRequired,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool
};

SelectForm.defaultProps = {
  placeholder: "Select an option",
};

export default SelectForm;
