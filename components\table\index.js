import React from "react";
import classes from "./styles.module.scss";
import PropTypes from 'prop-types';

const Table = ({ columns, rows, variant, second_class, ...props }) => {
    return (
        <table className={`${classes.custom_table} ${second_class} ${classes[variant]}`} {...props}>
            <thead>
                <tr>
                    {columns.map((column) => (
                        <th key={column.name}>{column.label}</th>
                    ))}
                </tr>
            </thead>
            <tbody>
                {rows.map((row) => {
                    const rowKey = row.id || row.key || row[columns[0].name];
                    return (
                        <tr key={rowKey}>
                            {columns.map((column) => (
                                <td key={column.name}>{row[column.name]}</td>
                            ))}
                        </tr>
                    );
                })}
            </tbody>
        </table>
    );
};

Table.propTypes = {
    columns: PropTypes.arrayOf(
        PropTypes.shape({
            label: PropTypes.string.isRequired,
            name: PropTypes.string.isRequired,
        })
    ).isRequired,
    rows: PropTypes.arrayOf(PropTypes.object).isRequired,
    variant: PropTypes.string,
    second_class: PropTypes.string,
};

export default Table;