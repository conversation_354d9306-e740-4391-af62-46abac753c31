"use client";
import React from "react";
import PropTypes from "prop-types";
import { toast } from "react-toastify";
import styles from "@/container/instituteDashboardContainer/registration/styles.module.scss"; 

const flattenErrors = (errors) => {
  const flat = [];
  const traverse = (obj) => {
    Object.values(obj).forEach((val) => {
      if (typeof val === "string") flat.push(val);
      else if (typeof val === "object") traverse(val);
    });
  };
  traverse(errors);
  return flat;
};

const PreviewTrigger = ({ dispatch, setShowPreview, termsAccepted, values, validateForm, updateFormAction }) => {
  const handlePreview = async () => {
    const errors = await validateForm(values);

    if (Object.keys(errors).length > 0) {
      const flatErrors = flattenErrors(errors);

      if (flatErrors.length > 0) {
        toast.error(flatErrors[0], { position: "top-center" });
      }
      return;
    }

    if (!termsAccepted) {
      toast.error("Please accept the Terms & Conditions before previewing.", {
        position: "top-center",
      });
      return;
    }

    // Update Redux form state
    dispatch(updateFormAction(values));
    setShowPreview(true);
  };

  return (
    <div className={styles.instructionButton}>
      <button
        type="button"
        onClick={handlePreview}
        disabled={!termsAccepted}
        className={!termsAccepted ? styles.disabledButton : ""}
      >
        PREVIEW
      </button>
    </div>
  );
};

PreviewTrigger.propTypes = {
  dispatch: PropTypes.func.isRequired,
  setShowPreview: PropTypes.func.isRequired,
  termsAccepted: PropTypes.bool.isRequired,
  values: PropTypes.object.isRequired,
  validateForm: PropTypes.func.isRequired,
  updateFormAction: PropTypes.func.isRequired,
};

export default PreviewTrigger;
