import classes from "./styles.module.scss";
import Image from "next/image";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';


const TopImageSlider = () => {
    return(
        <div className={classes.top_image_slider}>
            <div className={classes.top_image_slider_content}>
                <Swiper
                    modules={[Navigation, Pagination, Autoplay]}
                    slidesPerView={1}
                    navigation
                    pagination={{ clickable: true }}
                    autoplay={{ delay: 2000, disableOnInteraction: false }} 
                    loop={true}
                >
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Gate1.webp`} width={1600} height={647} alt="slide 1" />
                        </div>
                    </SwiperSlide>
                     <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-3.jpg`} width={1600} height={1066} alt="slide 1" />
                        </div>
                    </SwiperSlide>
                     <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} width={1280} height={960} alt="slide 1" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-1.jpg`} width={1280} height={853} alt="slide 17" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-2.jpg`} width={2400} height={1600} alt="slide 17" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-4.jpg`} width={1560} height={1040} alt="slide 2" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-5.jpg`} width={1560} height={1040} alt="slide 4" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-6.jpg`} width={1560} height={1040} alt="slide 6" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-7.jpg`} width={1560} height={1040} alt="slide 7" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-8.jpg`} width={1560} height={1040} alt="slide 8" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-9.jpg`} width={1560} height={1040} alt="slide 10" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}bssc/img-10.jpg`} width={1560} height={1040} alt="slide 11" />
                        </div>
                    </SwiperSlide>
                    <SwiperSlide>
                        <div className={classes.image_container}>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} width={2400} height={1800} alt="slide 12" />
                        </div>
                    </SwiperSlide>
                </Swiper>
            </div>
            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/rectangleTwo.webp`} width={2174} height={48} alt="rectangle" className={classes.rectangle_two} />
        </div>
    )
}

export default TopImageSlider;