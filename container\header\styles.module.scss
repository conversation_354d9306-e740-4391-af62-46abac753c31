@import "../../assets/css/global.scss";

.header_container {
    width: 100%;
    height: auto;

    .header_content {
        width: 100%;
        height: auto;

        // top section
        .top_section,
        .center_section,
        .current_date_time,
        .bottom_section {
            width: 100%;
            height: auto;
            background-color: $darkBrown;
            padding: 14px 88px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .ham_icon {
                display: none;
            }

            .top_left,
            .top_right {
                display: flex;
                align-items: center;
                gap: 20px;

                .contact_no,
                .email_id {
                    color: $white;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    font-family: $poppinsRegular400;
                    cursor: pointer;
                    text-decoration: unset;

                    .icon {
                        width: 16px;
                        height: auto;
                    }

                    &:hover {
                        opacity: 0.5;
                        scale: 0.99;
                    }
                }

                .login,
                .choose_language {
                    position: relative;
                    display: flex; // ✅ flex row
                    align-items: center;
                    gap: 10px; // space between welcome and buttons

                    .welcome_text {
                        color: $white;
                        font-size: 13px;
                        font-family: $poppinsRegular400;
                    }

                    .log_in_btn,
                    .choose_lang_btn,
                    .log_out_btn {
                        border: 1px solid #C26C31;
                        background-color: #3B1406;
                        border-radius: 6px;
                        padding: 4px 20px;
                        color: white;
                        font-size: 13px;
                        font-family: $poppinsRegular400;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        min-width: 90px;

                        &:hover {
                            opacity: 0.7;
                        }
                    }


                    .login_options,
                    .lang_option {
                        position: absolute;
                        top: calc(100% + 4px);
                        width: 100%;
                        border: 1px solid #C26C31;
                        background-color: #3B1406;
                        border-radius: 6px;

                        .nav_btn,
                        .lang_btn {
                            display: block;
                            width: 100%;
                            border-radius: 5px;
                            font-family: $poppinsRegular400;
                            font-size: 13px;
                            padding: 4px 10px;
                            background-color: transparent;
                            border: unset;
                            color: white;
                            cursor: pointer;

                            &:hover {
                                background-color: #C26C31;
                            }
                        }
                    }
                }
            }
        }


        // center section
        .center_section {
            background-color: $slowWhite;

            .left_logo,
            .right_logo {
                width: 100px;
                height: auto;
            }

            .center_center {
                .hindi_heading {
                    text-align: center;
                    font-size: 40px;
                    font-family: $rockWellExtraBold;
                    font-weight: 800;
                    color: $reddishBrown;
                }

                .english_heading {
                    text-align: center;
                    font-family: $robotoLight300;
                    color: $reddishBrown;
                    font-size: 30px;
                }

                .address {
                    text-align: center;
                    font-family: $poppinsRegular400;
                    color: $reddishBrown;
                    font-size: 19.5px;
                }
            }
        }

        .current_date_time {
            background-color: $slowWhite;
            border-top: 1px solid $reddishBrown;
            padding: 5px 88px;
            justify-content: space-between;
            gap: 20px;

            .date,
            .time {
                font-size: 15px;
                font-family: $robotoRegular400;
                color: $burntCoffee;

                span {
                    color: $reddishBrown;
                    font-family: $robotoMedium500;
                }
            }
        }

        .bottom_section {
            justify-content: center;

            .navigation_links {
                width: 100%;
                height: auto;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .close_icon {
                    display: none;
                }

                .menu_drp_dwn {
                    background-color: transparent;
                    border: unset;
                    cursor: pointer;
                    display: flex;
                    align-items: center;

                    svg {
                        margin-bottom: -2px;
                    }
                }

                a,
                .menu_drp_dwn {
                    color: $white;
                    font-family: $interRegular400;
                    font-size: 14px;
                    text-decoration: unset;
                    padding-bottom: 1px;
                    border-bottom: 2px solid transparent;
                    position: relative;

                    &::before {
                        content: "";
                        position: absolute;
                        width: 0%;
                        height: 1px;
                        background-color: white;
                        bottom: -2px;
                        left: 50%;
                        transform: translateX(-50%);
                        transition: 300ms ease-in-out;
                    }

                    &:hover {
                        &::before {
                            content: "";
                            position: absolute;
                            width: 100%;
                            height: 1px;
                            background-color: white;
                            bottom: -2px;
                            left: 50%;
                            transform: translateX(-50%);
                        }
                    }
                }

                .active {
                    &::before {
                        content: "";
                        position: absolute;
                        width: 100%;
                        height: 1px;
                        background-color: white;
                        bottom: -2px;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                }
            }
        }

        .bottom_submenu_section {
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 30px;
            padding: 15px;
            background-color: #833d235b;
            border-top: 1px solid #C26C31;
            border-bottom: 1px solid #C26C31;

            a {
                text-decoration: none;
                color: #3B1406;
                font-size: 14px;
                font-family: $poppinsMedium500;

                &:hover {
                    color: #833C23;
                }
            }
        }
    }
}

@media only screen and (max-width: 1366px) {
    .header_container {
        .header_content {

            .top_section,
            .center_section,
            .bottom_section {
                padding: 10px 65px;

                .top_left,
                .top_right {

                    .contact_no,
                    .email_id {
                        font-size: 14px;

                        .icon {
                            width: 19px;
                        }
                    }
                }
            }

            .center_section {

                .left_logo,
                .right_logo {
                    width: 90px;
                }

                .center_center {
                    .hindi_heading {
                        font-size: 36px;
                    }

                    .english_heading {
                        font-size: 27px;
                    }

                    .address {
                        font-size: 17px;
                    }
                }
            }

            .bottom_section {
                .navigation_links {
                    a {
                        font-size: 14px;
                        border-bottom: 2px solid transparent;
                    }
                }
            }

        }
    }
}

@media only screen and (max-width: 980px) {
    .header_container {
        .header_content {

            .top_section,
            .center_section,
            .bottom_section {
                padding: 10px 30px;
            }

            .center_section {

                .left_logo,
                .right_logo {
                    width: 80px;
                }

                .center_center {
                    .hindi_heading {
                        font-size: 32px;
                    }

                    .english_heading {
                        font-size: 24px;
                    }

                    .address {
                        font-size: 16px;
                    }
                }
            }

            .bottom_section {
                .navigation_links {
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 10px 30px;
                }
            }

        }
    }
}

@media only screen and (max-width: 667px) {
    .header_container {
        .header_content {

            .top_section,
            .center_section,
            .bottom_section {
                padding: 15px;

                .top_left,
                .top_right {
                    gap: 15px;

                    .contact_no,
                    .email_id {
                        display: none;
                    }
                }

                .ham_icon {
                    display: block;
                    color: $white;
                    font-size: 35px;
                }
            }

            .center_section {

                .left_logo,
                .right_logo {
                    width: 13vw;
                }

                .center_center {
                    .hindi_heading {
                        font-size: 4.5vw;
                    }

                    .english_heading {
                        font-size: 3.5vw;
                    }

                    .address {
                        font-size: 2.5vw;
                        margin-top: 4px;
                    }
                }
            }

            .bottom_section {
                width: 100%;
                height: 100%;
                overflow: hidden;
                overflow-y: auto;
                position: fixed;
                top: 0;
                left: -100%;
                z-index: 9;
                background-color: unset;
                background-image: url('https://prod-1.static.codebuckets.in/file/codebucket-production-public/bihar-sanskrit-siksha-board-frontend/images/bgOldPaper.webp');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                transition: 400ms ease-in-out;

                .navigation_links {
                    flex-wrap: wrap;
                    justify-content: center;
                    gap: 10px 30px;
                    width: 100%;
                    height: 100%;
                    flex-direction: column;
                    position: relative;

                    .close_icon {
                        display: block;
                        position: absolute;
                        right: 0;
                        top: 0;
                        font-size: 45px;
                        color: $reddishBrown;
                    }

                    a {
                        color: $reddishBrown;
                        font-family: $poppinsRegular400;
                        font-size: 16px;
                    }
                }
            }

            .bottom_section_open {
                left: 0;
            }
        }
    }
}

/* Navbar.css */

/* Parent menu */
.nav-item {
    position: relative;
}

.nav-link {
    padding: 10px 15px;
    cursor: pointer;
}

/* Submenu */
.submenu {
    position: absolute;
    top: 100%;
    /* places submenu right below */
    left: 0;
    min-width: 180px;
    background: #fff;
    border: 1px solid #ddd;
    display: none;
    z-index: 999;
}

/* Show submenu when hovering parent OR submenu */
.nav-item:hover .submenu,
.submenu:hover {
    display: block;
}

/* Submenu items */
.submenu a {
    display: block;
    padding: 10px;
    color: #333;
    text-decoration: none;
}

.submenu a:hover {
    background: #f0f0f0;
}

.dashboard_link {
  display: inline-block;
  padding: 4px 6px;
  margin-left: 10px;
  font-size: 13px;
  font-weight: 500;
  color: #fff; 
  border: 1px solid #C26C31;
  border-radius: 6px;
  background: #3B1406; 
  text-decoration: none;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.7;      }
}


                        