{"name": "bihar_sanskrit_shiksha_board", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:master": "env-cmd -f .buildParams.master next build", "build:stage": "env-cmd -f .buildParams.stage next build", "start:master": "env-cmd -f .buildParams.master next start", "start:stage": "env-cmd -f .buildParams.stage next start", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/x-date-pickers": "^8.11.0", "@reduxjs/toolkit": "^2.8.2", "@studio-freight/lenis": "^1.0.42", "axios": "^1.11.0", "env-cmd": "^10.1.0", "formik": "^2.4.6", "js-cookie": "^3.0.5", "lenis": "^1.3.8", "next": "15.4.4", "nookies": "^2.0.6", "notiflix": "^3.2.8", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "sass": "^1.89.2", "smooth-scrollbar": "^8.8.4", "swiper": "^11.2.10", "uuid": "^13.0.0", "yup": "^1.7.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.4.4"}}