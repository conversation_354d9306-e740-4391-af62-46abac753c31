@import "../../../assets/css/global.scss";

.digital_library_container {
  width: 100%;
  height: auto;
  min-height: 280px;
  padding: 20px 10px;

  .digital_library_content {
    width: 100%;
    height: auto;

    .digital_library_header {
      color: #571f0b;
      font-size: 24px;
      font-family: $poppinsSemiBold600;
      border-bottom: 2px solid #571f0b;
      padding-bottom: 8px;
      margin-bottom: 10px;
      display: inline-block;
    }

    .books_container {
      width: 100%;
      height: auto;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 10px 10px;

      .books_card {
        display: flex;
        align-items: center;
        flex-direction: column;

        .book_icon {
          width: 150px;
          height: auto;
        }

        .book_name {
          color: #571f0b;
          font-size: 12px;
          font-family: $poppinsMedium500;
        }

        .read_btn {
          border: unset;
          background-color: #571f0b;
          font-size: 11px;
          font-family: $poppinsMedium500;
          color: white;
          padding: 6px 14px;
          border-radius: 6px;
          margin-top: 15px;
          text-decoration: unset;
        }
      }
    }
  }
}
