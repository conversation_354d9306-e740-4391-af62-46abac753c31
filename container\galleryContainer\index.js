import { useState } from "react";
import classes from "./styles.module.scss";
import FolderCopyIcon from '@mui/icons-material/FolderCopy';
import RestoreRoundedIcon from '@mui/icons-material/RestoreRounded';
import GalleryCard from "@/components/galleryCard";


const GalleryContainer = () => {

    const [tab, setTab] = useState('tab1');

    return(
        <div className={classes.gallery_container}>
            <div className={classes.gallery_content}>
                <div className={classes.left_menu_container}>
                    <p className={classes.left_header}>Gallery</p>

                    <div className={classes.menus}>
                        <button className={classes.menu_btns}>
                            <FolderCopyIcon /> All Files
                        </button>
                        <button className={classes.menu_btns}>
                            <RestoreRoundedIcon /> All Files
                        </button>

                    </div>
                </div>
                <div className={classes.right_tabs_content}>
                    <div className={classes.top_tab_btns}>
                        <button 
                            className={`${classes.tab_btn} ${tab === "tab1" ? classes.active_tab_btn : "" }`}
                            onClick={() => setTab('tab1')}
                        >
                            All
                        </button>
                        <button 
                            className={`${classes.tab_btn} ${tab === "tab2" ? classes.active_tab_btn : "" }`}
                            onClick={() => setTab('tab2')}
                        >
                            Events
                        </button>
                        <button 
                            className={`${classes.tab_btn} ${tab === "tab3" ? classes.active_tab_btn : "" }`}
                            onClick={() => setTab('tab3')}
                        >
                            Institutions
                        </button>
                        <button 
                            className={`${classes.tab_btn} ${tab === "tab4" ? classes.active_tab_btn : "" }`}
                            onClick={() => setTab('tab4')}
                        >
                            Student Achievements
                        </button>
                        <button 
                            className={`${classes.tab_btn} ${tab === "tab5" ? classes.active_tab_btn : "" }`}
                            onClick={() => setTab('tab5')}
                        >
                            Workshops & Seminars
                        </button>
                        <button 
                            className={`${classes.tab_btn} ${tab === "tab6" ? classes.active_tab_btn : "" }`}
                            onClick={() => setTab('tab6')}
                        >
                            Cultural Activities
                        </button>
                    </div>
                    <div className={classes.tab_body}>
                        {tab === "tab1" &&
                            <div className={classes.cards_wrapper}>
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide1.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide13.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide2.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide4.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide6.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide7.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide8.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide9.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide10.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide11.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide12.webp`} />
                            </div> 
                        }
                        {tab === "tab2" &&
                            <div className={classes.cards_wrapper}>
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide1.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide13.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide2.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide4.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide6.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide7.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide8.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide9.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide10.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide11.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide12.webp`} />
                            </div> 
                        }
                        {tab === "tab3" &&
                            <div className={classes.cards_wrapper}>
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide1.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide13.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide2.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide4.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide6.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide7.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide8.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide9.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide10.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide11.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide12.webp`} />
                            </div> 
                        }
                        {tab === "tab4" &&
                            <div className={classes.cards_wrapper}>
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide1.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide13.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide2.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide4.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide6.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide7.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide8.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide9.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide10.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide11.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide12.webp`} />
                            </div> 
                        }
                        {tab === "tab5" &&
                            <div className={classes.cards_wrapper}>
                               <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide1.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide13.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide2.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide4.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide6.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide7.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide8.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide9.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide10.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide11.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide12.webp`} />
                            </div> 
                        }
                        {tab === "tab6" &&
                            <div className={classes.cards_wrapper}>
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide1.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide13.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide14.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide2.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide4.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide5.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide6.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide7.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide8.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide9.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide10.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide11.webp`} />
                                <GalleryCard gallery_image={`${process.env.NEXT_PUBLIC_CDN_URL}images/slide12.webp`} />
                            </div> 
                        }
                    </div>
                </div>
            </div>
        </div>
    )
}

export default GalleryContainer;