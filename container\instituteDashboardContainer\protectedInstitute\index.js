import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import PropTypes from "prop-types";

const ProtectedInstitutePage = ({ children }) => {
  const router = useRouter();
  const { educationLevelStatus, instituteDetails } = useSelector(
    (state) => state.auth
  );

  const [checkingAccess, setCheckingAccess] = useState(true);

  useEffect(() => {
    const hasMadhyama = Array.isArray(educationLevelStatus) && educationLevelStatus.includes("Madhyama");
    const isPrastavit = instituteDetails?.typeOfSchool === "प्रस्तावित";

    if (!hasMadhyama && !isPrastavit) {
      router.replace("/instituteDashboard");
    } else {
      setCheckingAccess(false);
    }
  }, [educationLevelStatus, instituteDetails, router]);

  if (checkingAccess) return null;

  return <>{children}</>;
};

ProtectedInstitutePage.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ProtectedInstitutePage;
