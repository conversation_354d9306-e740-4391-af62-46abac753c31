"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import Cookies from "js-cookie";
import { logout } from "@/features/auth/authSlice";

const SessionExpiredModal = () => {
  const [show, setShow] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const router = useRouter();
  const dispatch = useDispatch();

  useEffect(() => {
    setIsReady(true);
  }, []);

  useEffect(() => {
    if (!isReady) return;

    const isLoginPage = router.pathname === "/login";
    const sessionExpiredFlag = localStorage.getItem("sessionExpired");

    if (sessionExpiredFlag && !isLoginPage) {
      setShow(true); 
    } else if (sessionExpiredFlag && isLoginPage) {
      localStorage.removeItem("sessionExpired");
      setShow(false);
    }
  }, [isReady, router.pathname]);

  const handleLoginRedirect = () => {
    localStorage.clear();
    sessionStorage.clear();
    Cookies.remove("token");
    Cookies.remove("user_role");
    dispatch(logout());
    localStorage.removeItem("sessionExpired");
    router.push("/login");
  };

  if (!show) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        background: "rgba(0,0,0,0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
      }}
    >
      <div
        style={{
          background: "#fff",
          padding: "2rem",
          borderRadius: "8px",
          textAlign: "center",
          maxWidth: "400px",
        }}
      >
        <h2>Session Expired</h2>
        <p>Your session has expired. Please login again to continue.</p>
        <button
          onClick={handleLoginRedirect}
          style={{
            marginTop: "1rem",
            padding: "0.5rem 1rem",
            background: "#833C23",
            color: "#fff",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Login
        </button>
      </div>
    </div>
  );
};

export default SessionExpiredModal;
