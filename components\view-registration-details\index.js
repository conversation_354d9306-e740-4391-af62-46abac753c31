"use client";
import React from "react";
import PropTypes from "prop-types";
import styles from "./styles.module.scss";
import { useGetPaymentDetailsDropdownQuery } from "@/injectEndpoints/studentRegistrationendpoint";
import Image from "next/image";

const RegistrationPreview = ({ registration }) => {
  const {
    data: paymentDetails,
    isLoading,
    isError,
  } = useGetPaymentDetailsDropdownQuery(registration?.registrationNo || "", {
    skip: !registration,
  });

  if (!registration) return null;

  const paymentStatus =
    paymentDetails?.status?.toLowerCase() === "success" ? "Success" : "Failure";
  const paymentAmount = paymentDetails?.amountInr ?? "—";
  const transactionId = paymentDetails?.txnid ?? "—";
  const transactionDateTime = paymentDetails?.createdAt
    ? new Date(paymentDetails.createdAt).toLocaleString("en-IN", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      })
    : "—";

  return (
    <div className={styles.previewWrapper} id="registrationPrint">
      <div className={styles.center_section}>
        <Image
          src={`${process.env.NEXT_PUBLIC_CDN_URL}images/saraswatiMata2.webp`}
          width={180}
          height={181}
          alt="Saraswati Mata"
          className={styles.left_logo}
        />
        <div className={styles.center_center}>
          <h1 className={styles.hindi_heading}>बिहार संस्कृत शिक्षा बोर्ड</h1>
          <h2 className={styles.english_heading}>
            Bihar Sanskrit Shiksha Board
          </h2>
          <p className={styles.address}>17, बैक होर्डिंग रोड, पटना-800001</p>
        </div>
        <Image
          src={`${process.env.NEXT_PUBLIC_CDN_URL}images/bSLogo.webp`}
          width={180}
          height={180}
          alt="Board Logo"
          className={styles.right_logo}
        />
      </div>

      <hr className={styles.headerDivider} />

      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <p>
            <b>Registration Number:</b> {registration.registrationNo || "—"}
          </p>
        </div>
        <div className={styles.headerCenter}>
          <p>REGISTRATION-CUM-EXAMINATION FORM, MADHYAMA EXAM - 2026</p>
        </div>
        <div className={styles.headerRight}>
          <p>
            <b>Date of Registration:</b>{" "}
            {registration.createdDate
              ? new Date(registration.createdDate).toLocaleDateString("en-IN", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                })
              : "—"}
          </p>
        </div>
      </div>

      <hr className={styles.headerDivider} />

      <div className={styles.section}>
        <h3>Payment Information</h3>
        {isLoading && <p>⏳ Loading payment details...</p>}
        {isError && <p>❌ Failed to fetch payment details</p>}
        {!isLoading && !isError && paymentDetails ? (
          <div className={styles.paymentGrid}>
            <div className={styles.paymentLeft}>
              <p>
                <b>Payment Status:</b> {paymentStatus}
              </p>
              <p>
                <b>Transaction Date & Time:</b> {transactionDateTime}
              </p>
            </div>
            <div className={styles.paymentRight}>
              <p>
                <b>Payment Amount:</b> ₹{paymentAmount}
              </p>
              <p>
                <b>Transaction ID:</b> {transactionId}
              </p>
            </div>
          </div>
        ) : (
          !isLoading &&
          !isError &&
          !paymentDetails && <p>❌ No payment details found</p>
        )}
      </div>

      <div className={styles.section}>
        <h3>School Information</h3>
        <div className={styles.topInfo}>
          <p>
            <b>School Name:</b> {registration.schoolName || "—"}
          </p>
          <p>
            <b>School Code:</b> {registration.instituteCode || "—"}
          </p>
          <p>
            <b>District:</b> {registration.schoolDistrict || "—"}
          </p>
          <p>
            <b>School Category:</b> {registration.schoolCategoryName || "—"}
          </p>
          {registration.nodalSchool && (
            <p>
              <b>Nodal School:</b> {registration.nodalSchool}
            </p>
          )}
        </div>
      </div>

      <div className={styles.section}>
        <h3>Student Category</h3>
        <p>
          <b>Category:</b> {registration.studentCategory || "—"}
        </p>
        {registration.studentCategory === "Ex" && (
          <div className={styles.twoColumnLayout}>
            <p>
              <b>Old Registration Number:</b>{" "}
              {registration.oldRegistrationNumber || "N/A"}
            </p>
            <p>
              <b>Registration Year:</b> {registration.registrationYear || "N/A"}
            </p>
          </div>
        )}
        {registration.studentCategory === "Private" &&
          registration.supportingDocumentUrl && (
            <p>
              <b>Supporting Document:</b>{" "}
              <a
                href={registration.supportingDocumentUrl}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.fileLink}
              >
                View Document
              </a>
            </p>
          )}
      </div>

      <div className={styles.studentInfoWrapper}>
        <h3>Student Information</h3>
        <div className={styles.studentContent}>
          <div className={styles.basicDetails}>
            {[
              ["Student Name (English)", registration.studentNameEnglish],
              ["Student Name (Hindi)", registration.studentNameHindi],
              ["Father’s Name (English)", registration.fatherNameEnglish],
              ["Father’s Name (Hindi)", registration.fatherNameHindi],
              ["Mother’s Name (English)", registration.motherNameEnglish],
              ["Mother’s Name (Hindi)", registration.motherNameHindi],
              [
                "Date of Birth",
                registration.studentDob
                  ? new Date(registration.studentDob).toLocaleDateString(
                      "en-IN"
                    )
                  : "—",
              ],
              ["Gender", registration.studentGender],
              ["Mobile Number", registration.studentMobileNo],
              ["Email Address", registration.studentEmail],
              ["Caste Category", registration.studentCaste],
            ].map(([label, value]) => (
              <div className={styles.infoRow} key={label}>
                <p>
                  <b>{label}:</b> {value || "—"}
                </p>
              </div>
            ))}
          </div>

          <div className={styles.photoSection}>
            <Image
              src={registration.studentPictureUrl || "/placeholder-photo.png"}
              alt="Student"
              width={120}
              height={150}
              className={styles.photo}
            />
            <div className={styles.signature}>
              <Image
                src={
                  registration.studentSignatureUrl || "/placeholder-sign.png"
                }
                alt="Signature"
                width={120}
                height={50}
              />
              <p>Signature</p>
            </div>
          </div>
        </div>
      </div>

      <div className={`${styles.section} ${styles.pageBreakBefore}`}>

        <h3>Subjects</h3>
        <p>
          <b>Compulsory Subjects:</b>{" "}
          {registration.compulsorySubjects?.join(", ") || "N/A"}
        </p>
        <p>
          <b>Additional Subjects:</b>{" "}
          {registration.additionalSubjects?.join(", ") || "N/A"}
        </p>
      </div>

      <div className={styles.section}>
        <h3>Address Information</h3>
        <div className={styles.grid}>
          {[
            ["State", registration.studentState],
            ["District", registration.studentDistrict],
            ["Subdivision", registration.studentSubDivision],
            ["City", registration.studentCity],
            ["Village / Mohalla", registration.studentVillageMohalla],
            ["Area", registration.studentArea],
            ["Post Office", registration.studentPostOffice],
            ["Police Station", registration.studentPoliceStation],
            ["Landmark", registration.studentLandmark],
            ["Pincode", registration.studentPincode],
          ].map(([label, value]) => (
            <p key={label}>
              <b>{label}:</b> {value || "—"}
            </p>
          ))}
        </div>
      </div>

      <div className={styles.twoColumnLayout}>
        <div className={styles.section}>
          <h3>Nationality</h3>
          {(() => {
            const nationality = registration.nationality?.toLowerCase();
            if (nationality === "indian") {
              return (
                <>
                  <p>India</p>
                  <p>
                    <b>Aadhaar Number:</b> {registration.aadharNo || "N/A"}
                  </p>
                </>
              );
            } else if (nationality === "others") {
              return (
                <>
                  <p>{registration.countryName || "—"}</p>
                  <p>
                    <b>Foreign ID Type:</b>{" "}
                    {registration.countryIdType || "N/A"}
                  </p>
                  <p>
                    <b>Foreign ID Number:</b>{" "}
                    {registration.documentNumber || "N/A"}
                  </p>
                </>
              );
            } else {
              return <p>N/A</p>;
            }
          })()}
        </div>

        <div className={styles.section}>
          <h3>Handicapped Status</h3>
          <p>
            <b>Physically Handicapped:</b>{" "}
            {registration.physicallyHandicapStatus || "No"}
          </p>
          {registration.physicallyHandicapStatus === "Yes" && (
            <p>
              <b>Details:</b> {registration.handicappedDetails || "N/A"}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

RegistrationPreview.propTypes = {
  registration: PropTypes.object.isRequired,
};

export default RegistrationPreview;
