import React from "react";
import PropTypes from "prop-types";
import styles from "@/container/admin/styles.module.scss";

export default function AdminDashboardHeader({
  username,
  searchValue,
  setSearchValue,
}) {
  return (
    <div className={styles.header}>
      {/* Left: Greeting */}
      <div className={styles.greeting}>
        <h2>Welcome Back, {username}</h2>
        <p>Take control and kickstart your workflow for the day</p>
      </div>

      {/* Right: Search input with icon */}
      <div className={styles.searchBar}>
        <input
          type="text"
          placeholder="Search Your Content..."
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className={styles.searchInput}
        />
        <span
          className={`${styles.searchIcon} ${searchValue ? styles.hidden : ""}`}
        >
          🔍
        </span>
      </div>
    </div>
  );
}

AdminDashboardHeader.propTypes = {
  username: PropTypes.string.isRequired,
  searchValue: PropTypes.string.isRequired,
  setSearchValue: PropTypes.func.isRequired,
};
