"use client";
import React from "react";
import PropTypes from "prop-types";
import { useRouter } from "next/navigation";
import styles from "../styles.module.scss";

const SubmissionResult = ({ status, message, formNo }) => {
  const router = useRouter();
  if (!status) return null;
  

  return (

    

    <div
  className={`${styles.submissionResultContainer} ${
    status === "success" ? styles.success : styles.failure
  }`}
>

  <div className={styles.buttonRow}>
        {/* <div className={styles.halfButton}>
          <button
            type="button"
            onClick={() =>
              router.push("/instituteDashboard/registration/genralInstruction")
            }
          >
            CLICK HERE TO VIEW THE GENERAL INSTRUCTION / सामान्य निर्देश
          </button>
        </div> */}
        <div className={styles.halfButton}>
          <button type="button" onClick={() => router.push("/instituteDashboard")}>
            GO BACK TO DASHBOARD
          </button>
        </div>
      </div>
      
  <h3>Submission {status.toUpperCase()}</h3>
  <p>{message}</p>
  {formNo && (
    <p>
      <strong>Registration Number:</strong> {formNo}
    </p>
  )}
</div>

  );
};

SubmissionResult.propTypes = {
  status: PropTypes.string,
  message: PropTypes.string,
  formNo: PropTypes.string,
};

export default SubmissionResult;
