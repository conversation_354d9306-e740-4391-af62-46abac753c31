@import "../../../assets/css/global.scss";

.input_component {
  width: 100%;
  height: auto;
  
  .custom_input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    font-size: 16px;
    font-family: $poppinsRegular400;
    color: #333;
    border-radius: 6px;
    background-color: transparent;
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: #571f0b; 
    }
  }
}


@media (max-width: 768px) {
  .input_component {
    margin-bottom: 15px; 
  }

  .custom_input {
    padding: 12px; 
    font-size: 14px; 
  }
}

@media (max-width: 480px) {
  .input_component {
    margin-bottom: 10px;
  }

  .custom_input {
    padding: 10px;
    font-size: 13px; 
  }
}


.error {
  color: #e63946;   
  font-size:  14px;
  margin-top: 4px;
}
