import * as Yup from "yup";

const validationSchema = Yup.object().shape({
  address: Yup.object().shape({
    state: Yup.string().required("State is required"),
    district: Yup.string().required("District is required"),
    subDiv: Yup.string().required("Sub-Division is required"),
    area: Yup.string().nullable(),
    village: Yup.string().required("Village / Mohalla is required"),
    city: Yup.string().required("Town / City is required"),
    ps: Yup.string().nullable(),
    landmark: Yup.string().nullable(),
    po: Yup.string().nullable(),
    pincode: Yup.string()
      .matches(/^\d{6}$/, "Pincode must be exactly 6 digits")
      .required("Pincode is required"),
  }),
  nationality: Yup.string().required("Nationality is required"),
  aadhaar: Yup.string().when("nationality", {
    is: "Indian",
    then: (schema) =>
      schema
        .matches(/^\d{12}$/, "Aadhaar must be exactly 12 digits")
        .required("Aadhaar number is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  country: Yup.string().when("nationality", {
    is: "Others",
    then: (schema) => schema.required("Country name is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  foreignIdType: Yup.string().when("nationality", {
    is: "Others",
    then: (schema) => schema.required("ID Type is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  foreignIdNumber: Yup.string().when("nationality", {
    is: "Others",
    then: (schema) => schema.required("ID Number is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
  handicapped: Yup.string().required("Please select Yes or No"),
  handicappedDetails: Yup.array().when("handicapped", {
    is: "Yes",
    then: (schema) => schema.min(1, "Please select at least one special ability"),
    otherwise: (schema) => schema.max(0),
  }),
  studentName: Yup.object().shape({
    english: Yup.string()
      .matches(/^[A-Za-z\s]+$/, "Only alphabets allowed")
      .required("Student name is required"),
    hindi: Yup.string().required("Student name in Hindi is required"),
  }),
  fatherName: Yup.object().shape({
    english: Yup.string()
      .matches(/^[A-Za-z\s]+$/, "Only alphabets allowed")
      .required("Father name is required"),
    hindi: Yup.string().required("Father name in Hindi is required"),
  }),
  motherName: Yup.object().shape({
    english: Yup.string()
      .matches(/^[A-Za-z\s]+$/, "Only alphabets allowed")
      .required("Mother name is required"),
    hindi: Yup.string().required("Mother name in Hindi is required"),
  }),
  dob: Yup.date().required("Date of birth is required"),
  gender: Yup.string().required("Gender is required"),
  mobile: Yup.string()
    .matches(/^[6-9]\d{9}$/, "Enter a valid 10-digit mobile number")
    .length(10, "Mobile number must be exactly 10 digits")
    .required("Mobile number is required"),
  email: Yup.string()
    .trim()
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      "Enter a valid email address"
    ),
  caste: Yup.string().required("Category is required"),
  studentCategory: Yup.string()
    .oneOf(["Regular", "Private", "Ex"], "Invalid category")
    .required("Student Category is required"),
  oldRegNumber: Yup.string().when("studentCategory", {
    is: "Ex",
    then: (schema) => schema.notRequired(),
    otherwise: (schema) => schema.nullable(),
  }),
  regYear: Yup.string().when("studentCategory", {
    is: "Ex",
    then: (schema) =>
      schema
        .required("Registration Year is required")
        .matches(/^\d{4}$/, "Enter a valid year (YYYY)"),
    otherwise: (schema) => schema.nullable(),
  }),
  compulsorySubject: Yup.array()
    .of(Yup.string())
    .min(1, "At least one compulsory subject must be selected")
    .required("Compulsory subjects are required"),
  additionalSubject: Yup.array()
    .of(Yup.string())
    .min(1, "At least one additional subject is required")
    .required("Additional subjects are required"),
  uploads: Yup.object().shape({
    colorPhoto: Yup.mixed().required("Color photo is required"),
    studentSignature: Yup.mixed().required("Student signature is required"),
    supportingDocument: Yup.mixed().when("studentCategory", {
      is: "Private",
      then: (schema) => schema.required("Supporting document is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
  }),

  schoolCategory: Yup.string().required("School Category is required"),
  schoolName: Yup.string().required("School Name is required"),
  schoolCode: Yup.string().required("School Code is required"),
  district: Yup.string().required("District is required"),
  nodalSchool: Yup.string().when("showNodalSchool", {
    is: true,
    then: (schema) => schema.required("Nodal School is required"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export default validationSchema;
