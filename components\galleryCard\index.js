import React, { useState } from 'react';
import PropTypes from 'prop-types';
import classes from './styles.module.scss';
import Image from 'next/image';
import FavoriteBorderRoundedIcon from '@mui/icons-material/FavoriteBorderRounded';
import FavoriteRoundedIcon from '@mui/icons-material/FavoriteRounded';

const GalleryCard = ({ gallery_image, card_title, date }) => {
  const [isFavorite, setIsFavorite] = useState(false);

  const toggleFavorite = () => {
    setIsFavorite((prev) => !prev);
  };

  return (
    <div className={classes.gallery_card}>
      {isFavorite ? (
        <FavoriteRoundedIcon onClick={toggleFavorite} className={classes.icon_filled} />
      ) : (
        <FavoriteBorderRoundedIcon onClick={toggleFavorite} className={classes.icon_border} />
      )}

      <Image
        src={gallery_image}
        alt={card_title || 'Gallery Image'}
        className={classes.card_img}
        width={300}
        height={300}
      />

      <div className={classes.card_texts}>
        <p className={classes.card_title}>{card_title}</p>
        <p className={classes.date}>{date}</p>
      </div>
    </div>
  );
};

GalleryCard.propTypes = {
  gallery_image: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object,
  ]).isRequired,
  card_title: PropTypes.string.isRequired,
  date: PropTypes.string.isRequired,
};

export default GalleryCard;