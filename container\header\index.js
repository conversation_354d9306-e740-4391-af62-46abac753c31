"use client";
import React, { useState, useEffect, useRef } from "react";
import classes from "./styles.module.scss";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import { useDispatch, useSelector } from "react-redux";
import { logout } from "@/features/auth/authSlice";
import Cookies from "js-cookie";
import MenuRoundedIcon from "@mui/icons-material/MenuRounded";
import CloseRoundedIcon from "@mui/icons-material/CloseRounded";
import ArrowDropDownRoundedIcon from "@mui/icons-material/ArrowDropDownRounded";
import { LanguageSwitcher } from "../../components/translater";

const NAV_LINKS = [
  { href: "/", label: "Home" },
  { href: "/about-us", label: "About Us" },
  { href: "/institutions", label: "Institutions" },
  { type: "submenu", key: "examination", label: "Examination" },
  { href: "/digital-library", label: "Digital Library" },
  { type: "submenu", key: "quicklinks", label: "Quick Links" },
  { href: "#notification", label: "Notifications", isAnchor: true },
  { href: "/gallery", label: "Gallery" },
  { href: "/contact", label: "Contact" },
  { href: "/help", label: "Help" },
];

const SUBMENU_CONTENT = {
  academics: [
    { href: "", label: "Academics" },
    { href: "", label: "Admission" },
    { href: "", label: "Registration" },
    { href: "", label: "Admit card" },
    { href: "", label: "Notification" },
  ],
  examination: [
    { href: "/login?tab=institute", label: "Registration" },
    { href: "", label: "Admit card" },
    { href: "", label: "Result" },
  ],
  quicklinks: [{ href: "", label: "Links not available" }],
};

const Header = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { user, token, instituteDetails } = useSelector((state) => state.auth || {});

  const [mounted, setMounted] = useState(false);
  const [date, setDate] = useState("");
  const [time, setTime] = useState("");
  const [openHam, setOpenHam] = useState(false);
  const [submenu, setSubmenu] = useState("");
  const [showLogin, setShowLogin] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const menuRef = useRef(null);

  // Mount flag
  useEffect(() => setMounted(true), []);

  // Outside click
  useEffect(() => {
    if (!mounted) return;
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowLogin(false);
        setShowUserMenu(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [mounted]);

  // Date/time updater
  useEffect(() => {
    if (!mounted) return;
    const updateDateTime = () => {
      const now = new Date();
      setDate(now.toLocaleDateString("hi-IN"));
      setTime(now.toLocaleTimeString("hi-IN"));
    };
    updateDateTime();
    const timer = setInterval(updateDateTime, 1000);
    return () => clearInterval(timer);
  }, [mounted]);

  // Handlers
  const closeAllSubmenus = () => setSubmenu("");
  const toggleSubmenu = (menu) => setSubmenu((prev) => (prev === menu ? "" : menu));
  const handleLoginRedirect = (role) => {
    router.push(`/login?tab=${role}`);
    setShowLogin(false);
    closeAllSubmenus();
  };
  const handleLogout = () => {
    dispatch(logout());
    localStorage.clear();
    sessionStorage.clear();
    Object.keys(Cookies.get()).forEach((cookie) => Cookies.remove(cookie));
    router.push("/");
    setShowLogin(false);
    setShowUserMenu(false);
    closeAllSubmenus();
  };
  const goToDashboard = () => {
    if (user?.[0]?.userRole === "admin") router.push("/admin");
    else if (user?.[0]?.userRole === "institute") router.push("/instituteDashboard");
    else router.push("/");
    setShowUserMenu(false);
  };

  // Render auth menu
  const renderAuthMenu = () => {
    if (!mounted) {
      return (
        <button className={classes.log_in_btn} aria-expanded="false">
          Login <ArrowDropDownRoundedIcon />
        </button>
      );
    }
    if (token) {
      return (
        <>
          <button
            className={classes.log_in_btn}
            onClick={() => setShowUserMenu((prev) => !prev)}
            aria-expanded={showUserMenu}
            aria-haspopup="menu"
          >
            Welcome,&nbsp;{instituteDetails?.instituteName || user?.login}
            <ArrowDropDownRoundedIcon />
          </button>
          {showUserMenu && (
            <div className={classes.login_options} role="menu">
              <button className={classes.nav_btn} onClick={goToDashboard} role="menuitem">
                Dashboard
              </button>
              <button className={classes.nav_btn} onClick={handleLogout} role="menuitem">
                Logout
              </button>
            </div>
          )}
        </>
      );
    }
    return (
      <>
        <button
          className={classes.log_in_btn}
          onClick={() => setShowLogin((prev) => !prev)}
          aria-expanded={showLogin}
          aria-haspopup="menu"
        >
          Login <ArrowDropDownRoundedIcon />
        </button>
        {showLogin && (
          <div className={classes.login_options} role="menu">
            <button
              className={classes.nav_btn}
              onClick={() => handleLoginRedirect("institute")}
              role="menuitem"
            >
              Institute
            </button>
            <button
              className={classes.nav_btn}
              onClick={() => handleLoginRedirect("admin")}
              role="menuitem"
            >
              Admin
            </button>
          </div>
        )}
      </>
    );
  };

  // Render navigation
  const renderNavigation = () => {
    if (!mounted) return <div className={classes.bottom_section} />;
    if (token) return <div style={{ height: "0.9px", backgroundColor: "#833C23" }} />;

    return (
      <div className={`${classes.bottom_section} ${openHam ? classes.bottom_section_open : ""}`}>
        <nav className={classes.navigation_links}>
          <CloseRoundedIcon
            className={classes.close_icon}
            onClick={() => setOpenHam(false)}
          />
          {NAV_LINKS.map((link) => {
            if (link.type === "submenu") {
              return (
                <button
                  key={link.key}
                  className={classes.menu_drp_dwn}
                  onClick={() => toggleSubmenu(link.key)}
                >
                  {link.label} <ArrowDropDownRoundedIcon />
                </button>
              );
            }
            if (link.isAnchor) {
              return (
                <a key={link.href} href={link.href} onClick={closeAllSubmenus}>
                  {link.label}
                </a>
              );
            }
            return (
              <Link
                key={link.href}
                href={link.href}
                className={router.pathname === link.href ? classes.active : ""}
                onClick={closeAllSubmenus}
              >
                {link.label}
              </Link>
            );
          })}
        </nav>
      </div>
    );
  };

  const renderSubmenus = () => {
    if (!submenu) return null;
    return (
      <div className={classes.bottom_submenu_section}>
        {SUBMENU_CONTENT[submenu]?.map((item) => (
          <Link key={item.href + item.label} href={item.href} onClick={closeAllSubmenus}>
            {item.label}
          </Link>
        ))}
      </div>
    );
  };

  /** --- Main JSX --- */
  return (
    <header className={classes.header_container}>
      <div className={classes.header_content}>
        {/* Top */}
        <div className={classes.top_section}>
          <MenuRoundedIcon className={classes.ham_icon} onClick={() => setOpenHam((prev) => !prev)} />
          <div className={classes.top_left}>
            <a href="tel:+919472269757" className={classes.contact_no}>
              <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}icons/call.svg`} width={10} height={10} alt="call" className={classes.icon} />
              +91 9472269757
            </a>
            <a href="tel:+916122217880" className={classes.contact_no}>0612-2217880</a>
            <a href="mailto:<EMAIL>" className={classes.email_id}>
              <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}icons/email.svg`} width={10} height={10} alt="email" className={classes.icon} />
              <EMAIL>
            </a>
          </div>
          <div className={classes.top_right}>
            <div className={classes.login} ref={menuRef}>
              {renderAuthMenu()}
            </div>
            <LanguageSwitcher />
          </div>
        </div>

        {/* Center */}
        <div className={classes.center_section}>
          <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/saraswatiMata2.webp`} width={180} height={181} alt="Saraswati Mata" className={classes.left_logo} />
          <div className={classes.center_center}>
            <h1 className={classes.hindi_heading}>बिहार संस्कृत शिक्षा बोर्ड</h1>
            <h2 className={classes.english_heading}>Bihar Sanskrit Shiksha Board</h2>
            <p className={classes.address}>17, बैक होर्डिंग रोड, पटना-800001</p>
          </div>
          <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/bSLogo.webp`} width={382} height={360} alt="Board Logo" className={classes.right_logo} />
        </div>

        {/* Date/time */}
        <div className={classes.current_date_time}>
          {mounted ? (
            <>
              <p className={classes.date}>आज का दिनांक: <span>{date}</span></p>
              <p className={classes.time}>समय: <span translate="no">{time}</span></p>
            </>
          ) : (
            <>
              <p className={classes.date}>आज का दिनांक: <span /></p>
              <p className={classes.time}>समय: <span /></p>
            </>
          )}
        </div>

        {/* Navigation */}
        {renderNavigation()}

        {/* Submenus */}
        {renderSubmenus()}
      </div>
    </header>
  );
};

export default Header;
