"use client";
import React from "react";
import PropTypes from "prop-types";
import Image from "next/image";
import styles from "../styles.module.scss";
import Label from "@/components/formSection/label";

const RegistrationPreview = ({ registration, formData }) => {
  const data = registration || formData;
  if (!data) return null;

  const isPreview = !!formData;
  const category = data.studentCategory;
  const nationality = data.nationality;
  const isHandicapped = (data.physicallyHandicapStatus || data.handicapped) === "Yes";

  let details = [];
  if (Array.isArray(data.handicappedDetails)) {
    details = data.handicappedDetails;
  } else if (data.handicappedDetails) {
    details = [data.handicappedDetails];
  }

  const getPreviewUrl = (file) => {
    if (!file) return null;
    if (typeof file === "string") return file;
    if (file?.previewUrl) return file.previewUrl;
    if (file instanceof File || file instanceof Blob) return URL.createObjectURL(file);
    return null;
  };

  const photoUrl = getPreviewUrl(data.studentPictureUrl || data?.uploads?.colorPhoto);
  const signatureUrl = getPreviewUrl(data.studentSignatureUrl || data?.uploads?.studentSignature);
  const supportingDocUrl = getPreviewUrl(data.supportingDocumentUrl || data?.uploads?.supportingDocument);

  return (
    <div className={styles.previewContainer}>
      <div className={styles.header}>
        {isPreview ? (
          <h3>PREVIEW - REGISTRATION-CUM-EXAMINATION FORM, MADHYAMA EXAM - 2026</h3>
        ) : (
          <h2>
            REGISTRATION NO: <span>{data.registrationNo || "N/A"}</span>
          </h2>
        )}
      </div>

      {/* Main Info */}
      <div className={styles.mainContent}>
        <div className={styles.formSections}>
          <div className={styles.twoColumnLayout}>
            <Label label="1. STUDENT CATEGORY :" />
            <span>{category || "N/A"}</span>

            {category === "Ex" && (
              <div className={styles.section}>
                <Label label="Old Registration Number :" />
                <span>{data.oldRegistrationNumber || data?.oldRegNumber || "N/A"}</span>
                <Label label="Registration Year :" />
                <span>{data.registrationYear || data?.regYear || "N/A"}</span>
              </div>
            )}

            {category === "Private" && (
              <div className={styles.section}>
                <Label label="Supporting Document :" />
                {supportingDocUrl ? (
                  <a href={supportingDocUrl} target="_blank" rel="noopener noreferrer" className={styles.fileLink}>
                    View Supporting Document
                  </a>
                ) : (
                  <span>N/A</span>
                )}
              </div>
            )}
          </div>

          {/* School Info */}
          <div className={styles.section}>
            <Label label="2. SCHOOL CATEGORY :" />
            <span>{data.schoolCategoryName || data.schoolCategory || "N/A"} कोटि</span>
          </div>
          <div className={styles.section}>
            <Label label="3. SCHOOL NAME :" />
            <span>{data.schoolName || "N/A"}</span>
          </div>

          <div className={styles.section}>
            <Label label="4. SCHOOL CODE :" />
            <span>{data.schoolCode || "N/A"}</span>
          </div>

          <div className={styles.section}>
            <Label label="5. DISTRICT :" />
            <span>{data.studentDistrict || data.district || "N/A"}</span>
          </div>
          {data.nodalSchool && (
            <div className={styles.section}>
              <Label label=" NODAL SCHOOL :" />
              <span>{data.nodalSchool || "N/A"}</span>
            </div>
          )}
        </div>

        {/* Uploads */}
        <div className={styles.uploadColumn}>
          <div className={styles.previewImageColumn}>
            {photoUrl && (
              <div className={styles.previewImageBlock}>
                <div className={styles.previewImageWrapper}>
                  <Image
                    src={photoUrl}
                    alt="Color Photo"
                    fill
                    style={{ objectFit: "contain" }}
                  />
                </div>
                <Label label="Color Photo" />
              </div>
            )}

            {signatureUrl && (
              <div className={styles.previewImageBlock}>
                <div className={styles.previewImageWrapper}>
                  <Image
                    src={signatureUrl}
                    alt="Signature"
                    fill
                    style={{ objectFit: "contain" }}
                  />
                </div>
                <Label label="Student Signature" />
              </div>
            )}

            {!photoUrl && !signatureUrl && (
              <span className={styles.uploadFallback}>No uploads available</span>
            )}
          </div>

        </div>
      </div>

      {/* Student Info */}
      <div className={styles.fullWidthSection}>
        <div className={styles.twoColumnLayout}>
          <Label label="STUDENT INFO" />
          <div className={styles.section}>
            <Label label="Student Name :" />
            <span>
              {data.studentNameEnglish || data.studentName?.english || "—"} ||{" "}
              {data.studentNameHindi || data.studentName?.hindi || "—"}
            </span>
          </div>
          <div className={styles.section}>
            <Label label="Father Name :" />
            <span>
              {data.fatherNameEnglish || data.fatherName?.english || "—"} ||{" "}
              {data.fatherNameHindi || data.fatherName?.hindi || "—"}
            </span>
          </div>
          <div className={styles.section}>
            <Label label="Mother Name :" />
            <span>
              {data.motherNameEnglish || data.motherName?.english || "—"} ||{" "}
              {data.motherNameHindi || data.motherName?.hindi || "—"}
            </span>
          </div>
          <div className={styles.twoparts}>
            <div className={styles.section}>
              <Label label="Date of Birth :" />
              <span>
                {data.studentDob || data.dob
                  ? new Date(data.studentDob || data.dob).toLocaleDateString("en-IN", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                  })
                  : "—"}
              </span>
            </div>
            <div className={styles.section}>
              <Label label="Gender :" />
              <span>{data.studentGender || data.gender || "—"}</span>
            </div>
          </div>
          <div className={styles.twoparts}>
            <div className={styles.section}>
              <Label label="Mobile No :" />
              <span>{data.studentMobileNo || data.mobile || "—"}</span>
            </div>
            <div className={styles.section}>
              <Label label="Email ID :" />
              <span>{data.studentEmail || data.email || "—"}</span>
            </div>
          </div>
          <div className={styles.section}>
            <Label label="Category :" />
            <span>{category || data.caste || "—"}</span>
          </div>
        </div>

        {/* Subjects */}
        <div className={styles.section}>
          <Label label="Compulsory Subjects :" />
          <span>
            {Array.isArray(data.compulsorySubjects || data.compulsorySubject)
              ? (data.compulsorySubjects || data.compulsorySubject).join(", ")
              : "N/A"}
          </span>
        </div>
        <div className={styles.section}>
          <Label label="Additional Subjects :" />
          <span>
            {Array.isArray(data.additionalSubjects || data.additionalSubject)
              ? (data.additionalSubjects || data.additionalSubject).join(", ")
              : "None"}
          </span>
        </div>

        {/* Nationality & Handicapped */}
        <div className={styles.twoColumnLayout}>
          <Label label="Nationality :" />
          <span>{nationality === "Indian" ? "India" : nationality || "N/A"}</span>
          {nationality === "Indian" && (
            <div className={styles.section}>
              <Label label="Aadhaar :" />
              <span>{data.aadharNo || data.aadhaar || "N/A"}</span>
            </div>
          )}
          {nationality === "Others" && (
            <>
              <div className={styles.section}>
                <Label label="Country :" />
                <span>{data.countryName || data.country || "—"}</span>

              </div>
              <div className={styles.section}>
                <Label label="Foreign ID Type :" />
                <span>{data.countryIdType || data.foreignIdType || "N/A"}</span>
              </div>
              <div className={styles.section}>
                <Label label="Foreign ID Number :" />
                <span>{data.documentNumber || data.foreignIdNumber || "N/A"}</span>
              </div>
            </>
          )}
        </div>

        <div className={styles.twoColumnLayout}>
          <Label label="Handicapped :" />
          <div className={styles.section}>
            <span>
              {isHandicapped ? "Yes" : "No"}
              {isHandicapped && details.length > 0 && ` — ${details.join(", ")}`}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

RegistrationPreview.propTypes = {
  registration: PropTypes.object,
  formData: PropTypes.object,
};

export default RegistrationPreview;
