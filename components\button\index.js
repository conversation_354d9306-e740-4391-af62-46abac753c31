import classes from "./styles.module.scss";
import PropTypes from "prop-types";

const Button = ({
    icon,
    button_text,
    icon_two,
    second_class = "",
    onClick,
    variant = "default",
    type = "button",
    ...props
}) => {
    return (
        <button
            className={`${classes.normal_button} ${classes[variant] || ""} ${second_class}`}
            onClick={onClick}
            type={type} 
            {...props}
        >
            {icon}
            {button_text}
            {icon_two}
        </button>
    );
};

Button.propTypes = {
    button_text: PropTypes.string,
    variant: PropTypes.string,
    second_class: PropTypes.string,
    icon: PropTypes.node,
    icon_two: PropTypes.node,
    onClick: PropTypes.func,
    type: PropTypes.string,
};

export default Button;