'use client';
import { useEffect, useState } from 'react';
import { parseCookies, setCookie } from 'nookies';
import classes from "./styles.module.scss";
import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded'; // Assuming you're using MUI icons

const COOKIE_NAME = 'googtrans';

const LanguageSwitcher = () => {
  const [currentLanguage, setCurrentLanguage] = useState('');
  const [languageConfig, setLanguageConfig] = useState(null);
  const [showLang, setShowLang] = useState(false);

  useEffect(() => {
    const cookies = parseCookies();
    const existingLanguageCookieValue = cookies[COOKIE_NAME];
    console.log(existingLanguageCookieValue);
    
    let languageValue = '';

    if (existingLanguageCookieValue) {
      const sp = existingLanguageCookieValue.split('/');
      if (sp.length > 2) {
        languageValue = sp[2];
      }
    }

    if (global.__GOOGLE_TRANSLATION_CONFIG__ && !languageValue) {
      languageValue = global.__GOOGLE_TRANSLATION_CONFIG__.defaultLanguage;
    }

    if (languageValue) {
      setCurrentLanguage(languageValue);
    }

    if (global.__GOOGLE_TRANSLATION_CONFIG__) {
      setLanguageConfig(global.__GOOGLE_TRANSLATION_CONFIG__);
    }
  }, []);

  const setGoogTransCookie = (lang) => {
    const value = `/auto/${lang}`;
    const isLocalhost = window.location.hostname === 'localhost';

    // 1. Set cookie for current domain
    setCookie(null, COOKIE_NAME, value, {
      path: '/',
      maxAge: 365 * 24 * 60 * 60,
      sameSite: 'lax',
      secure: !isLocalhost
    });

    // 2. If not localhost, set cookie for base domain too
    if (!isLocalhost) {
      setCookie(null, COOKIE_NAME, value, {
        path: '/',
        maxAge: 365 * 24 * 60 * 60,
        sameSite: 'lax',
        secure: true,
        domain: process.env.NEXT_PUBLIC_COOKIE_PATH
      });
    }
  };

  const handleLanguageChange = (lang) => {
    setGoogTransCookie(lang);
    setCurrentLanguage(lang);
    setShowLang(false);
    window.location.reload();
  };


  const handleShowLangs = () => {
    setShowLang(prev => !prev);
  };

  if (!currentLanguage || !languageConfig) {
    return null;
  }

  // Find the current language title for the button label
  const currentLangTitle = languageConfig.languages.find(lang => lang.name === currentLanguage)?.title || currentLanguage;

  return (
    <div className={classes.choose_language}>
      <button className={classes.choose_lang_btn} onClick={handleShowLangs}>
        {currentLangTitle} <ArrowDropDownRoundedIcon />
      </button>

      {showLang && (
        <div className={classes.lang_option}>
          {languageConfig.languages.map(({ name, title }) => (
            <button
              key={name}
              className={classes.lang_btn}
              onClick={() => handleLanguageChange(name)}
              disabled={name === currentLanguage}
              style={{ fontWeight: name === currentLanguage ? 'bold' : 'normal' }}
            >
              {title}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export { LanguageSwitcher, COOKIE_NAME };