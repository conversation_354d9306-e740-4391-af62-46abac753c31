import { useState } from "react";
import classes from "./styles.module.scss";

const HistoryContainer = () => {

    const [tab, setTab] = useState('tab1');

    return(
        <div className={classes.history_container}>
            <div className={classes.history_content}>
                <div className={classes.history_header}>
                    <p>HISTORY OF SANSKRIT EDUCATION IN BIHAR</p>
                </div>

                <div className={classes.histories}>
                    {tab === "tab1" &&
                        <>
                            <p className={classes.paragraphs}>Before 1960 there was no Act with regard to Sanskrit Education in the State of Bihar. But provisions were made in the Bihar Education Code for this purpose. They are as follows:-</p>
                            <p className={classes.paragraphs}>Recognised Sanskrit Institutions are of two kinds,</p>
                            <p className={classes.paragraphs}>(i) Sanskrit Vidyalayas of Tols which prepares candidates for the Prathma, Madhyama, Shastri and <PERSON>charya's examination. (ii) Primary Sanskrit Schools which teaches sanskrit in addition to departmental and vernacular curriculum upto lower or upper primary section.</p>
                            <p className={classes.paragraphs}>The control of vidyalayas was exercised through the Bihar Sanskrit Association which conducted their examination. This Association consisted of a convocation and a council called respectively, the Sanskrit Parishad and Sanskrit Council, each body having same President and Secretary. The Sanskrit Parishad was a larger body consisting of 55 members whereas the Sanskrit Council was a smaller one consisting of 16 members only.</p>
                            <p className={classes.paragraphs}>The functions of the council, amongst others, were (a) to conduct sanskrit examinations (b) to issue title and certificates to be signed by the President and Secretary (c) to advise government in regard to:</p>
                            <p className={classes.paragraphs}>(i) The courses of studies to be followed for various sanskrit examination and in the various classes of sanskrit institutions.</p>
                            <p className={classes.paragraphs}>(ii) The rules to be framed for sanskrit examinations.</p>
                            <p className={classes.paragraphs}>(iii) Matters effecting sanskrit education generally.</p>
                            <p className={classes.paragraphs}>(iv) Recognise Tols and to propose for Govt. rules regarding their recognitions.</p>
                            <p className={classes.paragraphs}>The rules were framed for recognition of sanskrit vidyalayas. As per these rules no vidyalaya was to be considered elligible to</p>
                        </>
                    }


                    {tab === "tab2" &&
                        <>
                            <p className={classes.paragraphs}>present candidates at examination of the Sanskrit association, unless it was recognised by the Association. A vidyalaya seeking recognition was required to submit an application in the prescribed form.</p>
                            <p className={classes.paragraphs}>There were to be four examinations called Prathma, Madhyama, Shastri and Acharya.</p>
                            <p className={classes.paragraphs}><span>AFTER 1960</span></p>
                            <p className={classes.paragraphs}>The Kameshwar Singh Darbhanga Sanskrit Vishwavidyalaya Act, 1960 came into force. In this Act, Bihar Sanskrit Association was defined. Board of Sanskrit Education (Sanskrit Shiksha Parishad) was defined as Board, constituted by State Government for exercising control and superintendence over institutions other than tols imparting education upto Madhyama. Tol was defined as recognised Sanskrit Institation preparing candidates for Prathma, Madhyama, Shastri and Acharya or any other degree, under section 4 of the Act. The University was given power to give degrees and hold examinations.</p>
                            <p className={classes.paragraphs}>The university was given power of affiliation but no power of recognition. The senate and syndicate of the university were also not given the power of recognition of sanskrit schools upto Madhyama standard.</p>
                            <p className={classes.paragraphs}>Under section 44 of the Act the powers and duties of Bihar Sanskrit Association were to be exercised by the university or the Board of Sanskrit Education as Government might decide by order in writing.</p>
                            <p className={classes.paragraphs}>The University was never given authority to grant recognition to sanskrit institution upto Madhyama standard. This power always remained with Bihar Sanskrit Association. By this very section Bihar Sanskrit Association was dissolved and its powers and duties were to be exercised and performed by the Board of Sanskrit Education.</p>
                            <p className={classes.paragraphs}>Thereafter, vide State Government Notification No. 322 dated 24.1. 1961 the Bihar Sanskrit Board was constituted</p>
                        </>
                    }

                    {tab === "tab3" &&
                        <>
                            <p className={`${classes.paragraphs} ${classes.center}`}><span>SANSKRIT UNIVERSITY ACT OF 1962 (Act 21 of 1965)</span></p>
                            <p className={classes.paragraphs}>This Act by section 49 repealed the Act of 1960. In this Act also definition of Bihar Sanskrit Association, Board of Sanskrit Education (Sanskrit Shiksha Parishad), 'recognition and tol' remained more or less the same as in the earlier Act of 1960. In this Act also the university was not given the power of recognition of sanskrit school upto Madhyama standard. Although it was given a power of affiliation with previous approval of State Government. Under section 43 (2) of the Act, tols vidyalayas and Sanskrit High Schools recognised by Bihar Sanskrit Association before 1960 Act were entitled to sent-up candidates for Madhyama, Shastri and Acharya examinations of University.</p>
                            <p className={classes.paragraphs}>Under section 43 (3) Sanskrit High Schools which might have been recognised by the Board of Sanskrit Education, after the commencement of this Act or which was recognised, by same associations before such commencement of this Act were entitled to sent-up candidates of Madhyama examinations of the University.</p>
                            <p className={classes.paragraphs}><span>BIHAR STATE UNIVERSITIES ACT, 1976</span></p>
                            <p className={classes.paragraphs}>There came into operation Bihar State Universities Act 23 of 1976 with effect from 31.12.1976. This Act has repealed the earlier Sanskrit University Act 21 of 1965. This Act has confined the jurisdiction of the Sanskrit University to colleges imparting education above Madhyama Standard.</p>
                            <p className={classes.paragraphs}><span>BIHAR SANSKRIT SHIKSHA BOARD IS CONSTITUTED</span></p>
                            <p className={classes.paragraphs}>Under Bihar Sanskrit Shiksha Board Ordinance, the Bihar Sanskrit Shiksha Board has been constituted. This Ordinance has now been converted into Act 31 of 1982. In section 3 of this Act is the provision for establishment of the Board. This Act has been framed to provide for the constitution of an Autonomous Board for development and better supervision of sanskrit education upto</p>
                        </>
                    }


                   {tab === "tab4" &&
                        <>
                            <p className={classes.paragraphs}>Madhyama Standard in the State of Bihar. Under section 6 of the Act the Board has been given the power to direct, supervise and control sanskrit education upto Madhyama standard in the state and amongst others, the Board has been given the following powers:-</p>
                            <p className={classes.paragraphs}>(i) to grant recognition to sanskrit schools and tols upto Madhyama Standard with the prior approval of State Government;</p>
                            <p className={classes.paragraphs}>(ii) to withdraw recognitions of recognised sanskrit institutions;</p>
                            <p className={classes.paragraphs}>(iii) to maintain Register of recognised sanskrit schools and tols;</p>
                            <p className={classes.paragraphs}>(iv) to institute and conduct different sanskrit examinations upto Madhyama Standard;</p>
                            <p className={classes.paragraphs}>(v) to publish results of the examinations instituted by the Board and to award certificates, prizes and scholarships;</p>
                            <p className={classes.paragraphs}>(vi) to grant permission to candidates to appear at the examination upto Madhyama standard instituted by the Board and to refuse or withdraw such permission.</p>
                            <p className={classes.paragraphs}>The Sanskrit University as such had never the power of recognition of sanskrit schools upto Madhyama Standard.</p>
                            <p className={classes.paragraphs}>U/S 25 (4) of the Act all recognised schools and Tols schools be deemed to have been recognised by under Act until the expiration of the period of recognition subject, however, the power of the Board to withdraw recognition in accordance with the provisions of the Act.</p>
                            <p className={classes.paragraphs}>Similar provisions have been made in the ordinances as well.</p>
                        </>
                   }
                </div>

                <div className={classes.bottom_buttons}>
                    <button  
                        className={`${classes.tab_btn} ${tab === "tab1" ? classes.active : ''}`} 
                        onClick={() => setTab("tab1")} 
                    >
                        01
                    </button>
                    <button  
                        className={`${classes.tab_btn} ${tab === "tab2" ? classes.active : ''}`} 
                        onClick={() => setTab("tab2")} 
                    >
                        02
                    </button>
                    <button  
                        className={`${classes.tab_btn} ${tab === "tab3" ? classes.active : ''}`} 
                        onClick={() => setTab("tab3")} 
                    >
                        03
                    </button>
                    <button  
                        className={`${classes.tab_btn} ${tab === "tab4" ? classes.active : ''}`} 
                        onClick={() => setTab("tab4")} 
                    >
                        04
                    </button>
                </div>
            </div>
        </div>
    )
}

export default HistoryContainer;