$primary: #571f0b;
$border: #d9c6b6;
$bg: #fcf8f4;
$font-family: "Inter", sans-serif;

.sidebar {
  background: $primary;
  color: var(--text-light);
  width: 260px;
  min-height: 100vh;
  padding: 20px;
  margin-top: 50px;
  margin-left: 30px;
  margin-bottom: 50px;
  font-family: $font-family;
  border-radius: 20px;
}
.crownIcon {
  font-size: 30px;
  color: #fff;
}

/* Logo Section */
.logoRow {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  margin-bottom: 10px;
}

.logoIcon {
  font-size: 20px;
  color: #fff;
}

.logo {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

/* Divider */
.divider {
  border: none;
  border-top: 1px solid #fff;
  margin: 10px 0;
}

/* Profile Button */
.profileBtn {
  margin-top: 40px;
  background: transparent;
  border: 1px solid #fff;
  color: #fff;
  padding: 12px 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  width: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between; // ✅ aligns arrow to the right
  gap: 15px;
  font-weight: 500;
  font-size: 16px;
  transition: background 0.3s ease, color 0.3s ease;

  span {
    flex: 1; // ✅ pushes arrow to the end
  }

  .arrow {
    font-size: 12px;
    color: #fff;
  }

  &:hover {
    background: #fff;
    color: $primary;

    .arrow {
      color: $primary;
    }

    svg {
      color: $primary;
    }
  }
}

.profileIcon {
  font-size: 16px;
  color: inherit;
}

/* Navigation Section */
.nav {
  margin-top: 50px;
  color: #fff;
}

.sectionLabel {
  display: block;
  font-size: 14px;
  color: #f0e6dc;
  margin-bottom: 10px;
}

.navList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.navItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  margin: 20px 0;
  cursor: pointer;
  padding: 12px 12px;
  border-radius: 6px;
  width: 100%;
  color: #fff;
  font-weight: 400;
  background: transparent;
  transition: background 0.3s ease, color 0.3s ease;

  span {
    flex: 1;
  }

  .arrow {
    font-size: 12px;
    color: #fff;
  }

  &:hover {
    background: #fff;
    color: $primary;

    .arrow {
      color: $primary;
    }

    svg {
      color: $primary;
    }
  }
}

.navIcon {
  font-size: 18px;
  color: inherit;
}

/* Link styling to remove default styles */
.navLink {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  width: 100%;
  color: inherit;
  text-decoration: none;

  span {
    flex: 1;
  }

  .arrow {
    font-size: 12px;
    color: inherit;
  }
}

/* Active state styling */
.navItem.active {
  background: #fff;
  color: $primary;

  .arrow {
    color: $primary;
  }

  svg {
    color: $primary;
  }

  .navLink {
    color: $primary;
  }
}
