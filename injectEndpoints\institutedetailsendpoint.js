import { apiSlice } from "@/apiSlice/apiSlice";
import endpoints from "@/constants/endpoints";

export const instituteDetailsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getInstituteContacts: builder.query({
      query: () => ({
        url: endpoints.instituteContacts,
        method: "POST",
        body: { programCode: "LISTINSTITUTECONTACTS" },
      }),
      transformResponse: (response) => response?.data?.data || [],
      providesTags: [{ type: "InstituteContacts", id: "LIST" }],
    }),

    addInstituteContact: builder.mutation({
      query: (body) => ({
        url: endpoints.addInstituteContact,
        method: "POST",
        body: { programCode: "ADDINSTITUTECONTACTS", ...body },
      }),
      invalidatesTags: [{ type: "InstituteContacts", id: "LIST" }],
    }),

    updateInstituteContact: builder.mutation({
      query: (body) => ({
        url: endpoints.updateInstituteContact,
        method: "POST",
        body: { programCode: "UPDATEINSTITUTECONTACTS", ...body },
      }),
      invalidatesTags: [{ type: "InstituteContacts", id: "LIST" }],
    }),

    deleteInstituteContact: builder.mutation({
      query: (body) => ({
        url: endpoints.deleteInstituteContact,
        method: "POST",
        body: { programCode: "DELETEINSTITUTECONTACTS", ...body },
      }),
      invalidatesTags: [{ type: "InstituteContacts", id: "LIST" }],
    }),

    uploadInstitutePicture: builder.mutation({
      query: (body) => ({
        url: endpoints.uploadInstitutePicture,
        method: "POST",
        body: { programCode: "ADDINSTITUTEPICTURE", ...body },
      }),
      invalidatesTags: [{ type: "InstitutePictures", id: "LIST" }],
    }),

    listInstitutePictures: builder.query({
      query: () => ({
        url: endpoints.listInstitutePictures,
        method: "POST",
        body: { programCode: "LISTINSTITUTEPICTURE" },
      }),
      transformResponse: (response) => response?.data?.data || [],
      providesTags: [{ type: "InstitutePictures", id: "LIST" }],
    }),

    deleteInstitutePicture: builder.mutation({
      query: (body) => ({
        url: endpoints.deleteInstitutePicture,
        method: "POST",
        body: { programCode: "DELETEINSTITUTEPICTURE", ...body },
      }),
      invalidatesTags: [{ type: "InstitutePictures", id: "LIST" }],
    }),

    getNodalSchoolsDropdown: builder.query({
      query: (districtName) => ({
        url: endpoints.getDropdown,
        method: "POST",
        body: {
          dropdownType: "nodal_schools_dropdown",
          replacements: [districtName],
        },
      }),
      transformResponse: (response) => response?.data || [],
    }),

    getNodalSchoolId: builder.query({
      query: (schoolName) => ({
        url: endpoints.getDropdown,
        method: "POST",
        body: {
          dropdownType: "nodal_schools_id",
          replacements: [schoolName],
        },
      }),
      transformResponse: (response) => response?.data || null,
    }),


  }),
  overrideExisting: true,
});

export const {
  useGetInstituteContactsQuery,
  useAddInstituteContactMutation,
  useUpdateInstituteContactMutation,
  useDeleteInstituteContactMutation,
  useUploadInstitutePictureMutation,
  useListInstitutePicturesQuery,
  useDeleteInstitutePictureMutation,
  useGetNodalSchoolsDropdownQuery,
  useGetNodalSchoolIdQuery,
} = instituteDetailsApi;
