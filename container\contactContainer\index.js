import classes from "./styles.module.scss";
import Image from "next/image";
import CallRoundedIcon from '@mui/icons-material/CallRounded';
import EmailRoundedIcon from '@mui/icons-material/EmailRounded';
import LocationOnRoundedIcon from '@mui/icons-material/LocationOnRounded';

const ContactContainer = () => {
    return (
        <div className={classes.contact_container}>
            <div className={classes.contact_content}>

                <div className={classes.content}>
                    <div className={classes.left}>
                        <p className={classes.contact_us_header}>Contact Us</p>

                        <div className={classes.contact_details}>
                            <a href="tel:+919472269757" className={classes.a}>
                                <CallRoundedIcon /> +91 9472269757
                            </a>
                            <a href="tel:+916122217880" className={classes.a}>
                                <CallRoundedIcon /> 0612-2217880
                            </a>
                            <a href="mailto:<EMAIL>" className={classes.a}>
                                <EmailRoundedIcon /> <EMAIL>
                            </a>
                            <a
                                href="https://www.google.com/maps/dir//17,Back,+Harding+Rd,+Bihar+800014/@25.5911831,85.0207462,32537m/data=!3m1!1e3!4m8!4m7!1m0!1m5!1m1!1s0x39ed57f9b8557dbd:0x3f5e9140f713c496!2m2!1d85.1031477!2d25.5912062?entry=ttu"
                                className={classes.a}
                                target="_blank" rel="noopener noreferrer"
                            >
                                <LocationOnRoundedIcon /> 17,Back, Harding Rd Patna, 800001, Bihar
                            </a>
                        </div>

                        <div className={classes.social_icons}>
                            <a href="https://www.instagram.com/bssb.patna" target="_blank"><Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/instagram.svg`} width={20} height={20} alt="Instagram" className={classes.s_icon} /></a>
                            <a href="https://www.facebook.com/bssbpatna/" target="_blank"><Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/facebook.svg`} width={20} height={20} alt="Facebook" className={classes.s_icon} /></a>
                            <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/twitter.svg`} width={20} height={20} alt="Twitter" className={classes.s_icon} />
                        </div>
                    </div>

                    <div className={classes.right}>
                        <div className={classes.map_container}>
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4427.733934832494!2d85.10057277606326!3d25.591211015627813!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39ed57f9b8557dbd%3A0x3f5e9140f713c496!2sBihar%20Sanskrit%20Shiksha%20Board!5e1!3m2!1sen!2sin!4v1755080280405!5m2!1sen!2sin"
                                allowFullScreen
                                loading="lazy"
                                referrerPolicy="no-referrer-when-downgrade"
                                title="Map showing location of Bihar Sanskrit Shiksha Board"
                            ></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactContainer;