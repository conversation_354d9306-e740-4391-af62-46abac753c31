import { createSlice } from "@reduxjs/toolkit";
import Cookies from "js-cookie";

const initialState = {
  user: null,
  permissions: [],
  instituteDetails: null,
  educationLevelStatus: [],
  token: null,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state, action) => {
      const { data } = action.payload;
      state.user = data?.user || null;
      state.permissions = data?.permissions || [];
      state.instituteDetails = data?.instituteDetails || null;
      state.educationLevelStatus = data?.educationLevelStatus || [];
      state.token = data?.token || null;

      if (typeof window !== "undefined") {
        localStorage.setItem("user", JSON.stringify(state.user));
        localStorage.setItem("permissions", JSON.stringify(state.permissions));
        localStorage.setItem("instituteDetails", JSON.stringify(state.instituteDetails));
        localStorage.setItem("educationLevelStatus", JSON.stringify(state.educationLevelStatus));
        localStorage.setItem("token", state.token);

        Cookies.set("token", state.token, { sameSite: "Lax" });
        Cookies.set("user_role", state.user?.[0]?.userRole || "", { sameSite: "Lax" });
      }
    },

    logout: (state) => {
      state.user = null;
      state.permissions = [];
      state.instituteDetails = null;
      state.educationLevelStatus = [];
      state.token = null;

      if (typeof window !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
        Cookies.remove("token");
        Cookies.remove("user_role");
      }
    },
  },
});

export const { setCredentials, logout } = authSlice.actions;
export default authSlice.reducer;
