"use client";
import React, { useState } from "react";
import Pagination from "@/components/admin/pagination";
import styles from "./styles.module.scss";

export default function InstitutionsContainer() {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const {
    data: notificationsData,
    error,
    isLoading: isFetchingNotifications,
  } = useGetNotificationsQuery({
    programCode: "LISTNOTIFICATION",
    notificationId: "%%",
  });

  const notifications = notificationsData?.data?.data || [];
  const totalItems = notificationsData?.data?.totalCount || 0;

  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentNotifications = notifications.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  if (isFetchingNotifications) {
    return <div>Loading institutions...</div>;
  }

  if (error) {
    return <div>Error loading institutions: {error.message}</div>;
  }

  return (
    <div className={styles.notifications}>
      <div className={styles.header}>
        <h3 className={styles.title}>View Institutions:</h3>
        <button className={styles.addBtn} onClick={openFileupload}>
          &nbsp; Upload New
        </button>
      </div>

      <div className={styles.list}>
        {currentNotifications.length > 0 ? (
          currentNotifications.map((note) => (
            <div
              key={note.notificationId}
              className={styles.item}
              id={`notification-${note.notificationId}`}
              data-id={note.notificationId}
            >
              <p>{note.notificationText}</p>
              <div className={styles.actions}>
                <a
                  href={note.notificationFileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.edit}
                >
                  View
                </a>
              </div>
            </div>
          ))
        ) : (
          <p className={styles.empty}>No institutions found.</p>
        )}
      </div>

      <Pagination
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
      />
    </div>
  );
}
