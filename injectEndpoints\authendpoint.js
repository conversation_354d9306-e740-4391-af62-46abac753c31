import { apiSlice } from "@/apiSlice/apiSlice";
import endpoints from "@/constants/endpoints"

export const authApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (credentials) => ({
        url: endpoints.login, 
        method: "POST",
        body: credentials,
      }),
    }),
  }),
});

export const { useLoginMutation } = authApi;


    // "login":"10301500703",
    // "password":"zIloZLf4",
  	// "user_role" : "institute"