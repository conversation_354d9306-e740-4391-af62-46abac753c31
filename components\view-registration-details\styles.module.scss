@import "../../assets/css/global.scss"; .previewWrapper {
  min-height: 297mm;
  margin: auto;
  padding: 25px 35px;
  background: #fff;
  font-family: "Times New Roman", serif;
  font-size: 14px;
  line-height: 1.5;
  color: #000;
  border: 1px solid #ccc;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;

  .headerLeft,
  .headerRight {
    flex: 1;
    p {
      margin: 2px 0;
      font-size: 14px;
    }
  }

  .headerCenter {
    flex: 2;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.4;
    p {
      margin: 0;
    }
  }
}

.headerDivider {
  border: 0;
  border-top: 1.5px solid #000;
  margin: 5px 0 15px;
}

.section {
  margin-bottom: 25px;

  h3 {
    font-size: 15px;
    font-weight: 700;
    margin-bottom: 10px;
    border-bottom: 1px solid #000;
    padding-bottom: 3px;
  }

  p {
    margin: 4px 0;
    font-size: 14px;
    b {
      font-weight: 600;
    }
  }
}

.paymentGrid {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;

  .paymentLeft,
  .paymentRight {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
}

.topInfo,
.studentCategorySection {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.studentInfoWrapper {
  margin: 20px 0;

  h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
    border-bottom: 1px solid #000;
    padding-bottom: 3px;
  }

  .studentContent {
    display: flex;
    justify-content: space-between;
    gap: 30px;
  }

  .basicDetails {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .infoRow {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px 20px;

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.4;

        b {
          font-weight: 600;
        }
      }
    }
  }

  .photoSection {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .photo {
      width: 140px;
      height: 180px;
      object-fit: cover;
      border: 1px solid #000;
      padding: 2px;
    }

    .signature {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      margin-top: 10px;

      img {
        width: 120px;
        border-top: 1px solid #000;
        padding-top: 2px;
      }

      p {
        font-size: 12px;
        margin: 0;
      }
    }
  }
}


.twoColumnLayout {
  display: flex;
  justify-content: space-between;
  gap: 25px;
  margin-top: 15px;

  .section {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fafafa;

    h3 {
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 6px;
      border-bottom: 1px solid #ccc;
      padding-bottom: 3px;
    }

    p {
      margin: 3px 0;
      font-size: 14px;

      b {
        font-weight: 600;
      }
    }
  }
}

.center_section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin-bottom: 20px;
}

.hindi_heading {
    text-align: center;
    font-size: 40px;
    font-family: $rockWellExtraBold;
    font-weight: 800;
    color: $reddishBrown;
}

.english_heading {
    text-align: center;
    font-family: $robotoLight300;
    color: $reddishBrown;
    font-size: 30px;
}

.address {
    text-align: center;
    font-family: $poppinsRegular400;
    color: $reddishBrown;
    font-size: 19.5px;
}
   
.left_logo,
.right_logo {
    width: 100px;
    height: auto;
}


@media print {
  @page {
    margin: 0;
  }

  .pageBreakBefore {
    page-break-before: auto;
    margin-top: 20px !important; 
  }
}
