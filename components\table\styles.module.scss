@import "../../assets/css/global.scss";

.custom_table{
    width: 100%;
    height: auto;
    border-collapse: collapse;
}

.table_one{
    overflow: hidden;

    thead{
        background-color: #571F0B;
        border-radius: 12px;
        
        tr{
            th{
                font-weight: unset;
                font-style: unset;
                font-family: $poppinsRegular400;
                color: white;
                font-size: 18px;
                padding: 13px;
            }
            th:first-child {
                border-radius: 8px 0px 0px 8px;
            }
            th:last-child {
                border-radius: 0px 8px 8px 0px;
            }
        }
    }

    tbody{
        tr{
            border-bottom: 1px solid #571F0B;

            td{
                font-size: 18px;
                color: #571F0B;
                padding: 20px 15px;
                text-align: center;
                font-family: $poppinsRegular400;
                
                .arrow{
                    width: 12px;
                    height: auto;
                }
            }
        }
    }
}