import classes from "./styles.module.scss";
import Image from "next/image";

const Politicians = () => {
    return(
        <div className={classes.politicians}>
            <div className={classes.politicians_content}>
                <div className={classes.side_content}>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/NitishKumar.webp`} width={477} height={466} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}>श्री नीतीश कुमार</p>
                            <p className={classes.position}>मुख्यमंत्री</p>
                        </div>
                    </div>
                </div>
                <div className={classes.middle_content}>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/SamratChoudhary.webp`} width={1200} height={720} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}>श्री सम्राट चौधरी</p>
                            <p className={classes.position}>उपमुख्यमंत्री</p>
                        </div>
                    </div>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Bijay.webp`} width={236} height={214} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}>श्री विजय कुमार सिन्हा</p>
                            <p className={classes.position}>उपमुख्यमंत्री</p>
                        </div>
                    </div>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/Sunil.webp`} width={300} height={275} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}>श्री सुनील कुमार सिंह</p>
                            <p className={classes.position}>शिक्षा मंत्री</p>
                        </div>
                    </div>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}ChatGPTImg.png`} width={160} height={160} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}><span translate="no">श्री (डॉ.) बी. राजेन्द्र</span> </p>
                            <p className={classes.position}>अपर मुख्य सचिव</p>
                        </div>
                    </div>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/nirajKumar23.webp`} width={310} height={312} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}>श्री नीरज कुमार </p>
                            <p className={classes.position}>सचिव (BSSB)</p>
                        </div>
                    </div>
                </div>
                <div className={classes.side_content}>
                    <div className={classes.politicians_card}>
                        <Image src={`${process.env.NEXT_PUBLIC_CDN_URL}images/mruntujaya2.webp`} width={512} height={486} alt="politicians" className={classes.politicians_image} />
                        <div className={classes.name_position}>
                            <p className={classes.name}>श्री मृत्युंजय कुमार झा</p>
                            <p className={classes.position}>अध्यक्ष (BSSB)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Politicians;