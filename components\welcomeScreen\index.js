import React, { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import styles from './styles.module.scss';
import Image from 'next/image';
import WelcomeBorder from '../../assets/images/welcomeOne.webp';

const WelcomeScreen = ({ open }) => {
	const [showNextButton, setShowNextButton] = useState(false);
	const [hideStart, setHideStart] = useState(false);
	const [unwrap, setUnwrap] = useState(false);

	// Generate stable IDs once using useMemo
	const curtainUnits = useMemo(() => {
		return Array.from({ length: 15 }, (_, i) => ({
			id: `curtain-unit-${i}`,
			delay: `-${(i + 1) * 0.1}s`,
		}));
	}, []);

	const handleWrap = () => {
		setHideStart(true);
		setShowNextButton(true);
		setTimeout(() => {
			setUnwrap(true);
		}, 1200);
	};

	return (
		<div className={styles.rxWorld}>
			<section className={styles.rnOuter}>
				<section className={styles.aoTable}>
					<div className={styles.aoTableCell}></div>
				</section>

				{!hideStart && (
					<button className={styles.btn} onClick={handleWrap}>
						शुभ आरंभ
					</button>
				)}

				{showNextButton && (
					<div className={styles.btnNext}>
						<div className={styles.text_wrapper}>
							<Image src={WelcomeBorder} alt="border" className={styles.welcome_img} />
							<button onClick={open} className={styles.animated_gold_text}>
								कृपया वेबसाइट पर जाएँ
							</button>
							<Image src={WelcomeBorder} alt="border" className={styles.welcome_img_two} />
						</div>
					</div>
				)}

				<div className={styles.rnInner}>
					<div className={`${styles.rnLeft} ${unwrap ? styles.openLeft : ''}`}>
						{curtainUnits.map((unit) => (
							<div
								key={`left-${unit.id}`}
								className={styles.rnUnit}
								style={{ animationDelay: unit.delay }}
							/>
						))}
					</div>
					<div className={`${styles.rnRight} ${unwrap ? styles.openRight : ''}`}>
						{curtainUnits.map((unit) => (
							<div
								key={`right-${unit.id}`}
								className={styles.rnUnit}
								style={{ animationDelay: unit.delay }}
							/>
						))}
					</div>
				</div>
			</section>
		</div>
	);
};

WelcomeScreen.propTypes = {
	open: PropTypes.bool,
};

export default WelcomeScreen;