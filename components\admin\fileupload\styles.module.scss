// FileUploadModal.module.scss

// SCSS variables
$primary: #571f0b;
$border: #d9c6b6;
$font-family: "Inter", sans-serif;
$secondary-color: #0d45a5;
$bg-color: #fff;
$shadow-color: rgb(218, 229, 255);
$border-color: rgb(159, 159, 160);
$text-color: #000;
$light-text-color: rgb(105, 105, 105);
$drop-border-color: rgb(171, 202, 255);
$hover-drop-bg: rgba(0, 140, 255, 0.164);
$drop-hover-border: rgba(17, 17, 17, 0.616);
$button-hover-bg: #0d45a5;

// Modal Overlay
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

// Reusable styles for form and elements
.form {
  background-color: $bg-color;
  box-shadow: 0 10px 60px $shadow-color;
  border: 1px solid $border-color;
  border-radius: 20px;
  padding: 2rem 0.7rem 0.7rem 0.7rem;
  text-align: center;
  font-size: 1.125rem;
  max-width: 320px;
  position: relative;

  &-title {
    color: $text-color;
    font-size: 1.8rem;
    font-weight: 500;
  }

  &-paragraph {
    margin-top: 10px;
    font-size: 0.9375rem;
    color: $light-text-color;
  }

  // Input field for advertisement number
  &-input {
    margin-top: 15px;
    padding: 10px;
    width: calc(100% - 20px);
    border: 1px solid $border-color;
    border-radius: 8px;
    font-size: 1rem;
    box-sizing: border-box;
  }
}

.dropContainer {
  background-color: $bg-color;
  position: relative;
  display: flex;
  gap: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px;
  margin-top: 2.1875rem;
  border-radius: 10px;
  border: 2px dashed $drop-border-color;
  color: #444;
  cursor: pointer;
  transition: background 0.2s ease-in-out, border 0.2s ease-in-out;

  &:hover {
    background: $hover-drop-bg;
    border-color: $drop-hover-border;
  }

  .dropTitle {
    color: #444;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    transition: color 0.2s ease-in-out;
  }
}

// File input styling
.fileInput {
  width: 350px;
  max-width: 100%;
  color: #444;
  padding: 2px;
  background: $bg-color;
  border-radius: 10px;
  border: 1px solid rgba(8, 8, 8, 0.288);

  &::file-selector-button {
    margin-right: 20px;
    border: none;
    background: $primary;
    padding: 10px 20px;
    border-radius: 10px;
    color: #fff;
    cursor: pointer;
    transition: background 0.2s ease-in-out;

    &:hover {
      background: $secondary-color;
    }
  }
}

// Button group and individual button styles
.buttonGroup {
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
  gap: 10px;
  padding: 0 10px;
}

.cancelBtn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease-in-out;
  background-color: #e0e0e0;
  color: #333;
  &:hover {
    background-color: #d5d5d5;
  }
}

.submitBtn {
  flex: 1;
  padding: 10px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease-in-out;
  background-color: $primary;
  color: #fff;
  &:hover {
    background-color: $secondary-color;
  }
}
