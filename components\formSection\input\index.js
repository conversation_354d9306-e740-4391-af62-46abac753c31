"use client";
import React from "react";
import PropTypes from "prop-types";
import { useField } from "formik";
import Label from "../label";
import classes from "./styles.module.scss";

const Input = ({ label, className, id, required, ...props }) => {
  const [field, meta] = useField(props);

  return (
    <div className={classes.input_component}>
      {label && (
        <Label
          label={label + (required ? " *" : "")} 
          htmlFor={id || props.name}
          className={classes.label}
        />
      )}

      <input
        {...field}
        {...props}
        id={id || props.name}
        className={`${classes.custom_input} ${className} ${
          meta.touched && meta.error ? classes.input_error : ""
        }`}
      />

      {meta.touched && meta.error && (
        <span className={classes.error}>{meta.error}</span>
      )}
    </div>
  );
};

Input.propTypes = {
  label: PropTypes.string,
  type: PropTypes.string,
  name: PropTypes.string.isRequired,
  id: PropTypes.string,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  required: PropTypes.bool, // ✅ new prop
};

Input.defaultProps = {
  type: "text",
  label: "",
  id: undefined,
  placeholder: "",
  className: "",
  required: false,
};

export default Input;
